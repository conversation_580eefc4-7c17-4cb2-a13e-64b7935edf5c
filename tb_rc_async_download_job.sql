/*
 Navicat MySQL Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 50743
 Source Host           : ************:33060
 Source Schema         : j_cloud_saas_rc_new

 Target Server Type    : MySQL
 Target Server Version : 50743
 File Encoding         : 65001

 Date: 23/01/2025 17:29:24
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for tb_rc_async_download_job
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_async_download_job`;
CREATE TABLE `tb_rc_async_download_job`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `state` tinyint(2) NOT NULL DEFAULT 0 COMMENT '下载状态',
  `apply_time` datetime(0) NOT NULL COMMENT '申请时间',
  `file_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径',
  `request_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求的内容',
  `worker` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '处理器',
  `file_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名',
  `type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件类型',
  `apply_user_id` bigint(19) NULL DEFAULT NULL COMMENT '申请用户id',
  `apply_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申请用户名',
  `bucket_name` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'oss_bucket',
  `object_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'oss.objectName',
  `resource` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '异步下载任务' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_alipay_bank_consume_bill
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_alipay_bank_consume_bill`;
CREATE TABLE `tb_rc_bill_alipay_bank_consume_bill`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `activity_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动ID',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型',
  `consumer_time` datetime(0) NULL DEFAULT NULL COMMENT '核销时间',
  `consumer_face_value` decimal(19, 2) NULL DEFAULT NULL COMMENT '红包核销面额（元）',
  `order_amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '订单金额（元）',
  `trade_amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '实付金额（元）',
  `bank_response_serial_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行支付流水',
  `alipay_trade_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付宝交易号',
  `merchant_pid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户PID',
  `pre_charge_mode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预充值方式',
  `merchant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家名称',
  `store_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店id',
  `store_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店名称',
  `coupon_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券ID',
  `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板id',
  `bank_card_cornet` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡的前六后四位',
  `business_district_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商圈名称',
  `business_district_store_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商圈名称',
  `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归属方ID',
  `owner_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归属方名称',
  `config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方ID',
  `config_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方名称',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appId',
  `funding_pool` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金池ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `consumer_time_idx`(`consumer_time`) USING BTREE,
  INDEX `coupon_id_idx`(`coupon_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付宝银行消费账单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_alipay_bank_refund_bill
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_alipay_bank_refund_bill`;
CREATE TABLE `tb_rc_bill_alipay_bank_refund_bill`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `activity_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动ID',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型',
  `refund_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款时间',
  `consumer_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销时间',
  `consumer_face_value` decimal(19, 2) NULL DEFAULT NULL COMMENT '红包核销面额（元）',
  `refund_face_value` decimal(19, 2) NULL DEFAULT NULL COMMENT '红包核销面额（元）',
  `order_amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '订单金额（元）',
  `trade_amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '实付金额（元）',
  `bank_response_serial_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行支付流水',
  `alipay_trade_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付宝交易号',
  `merchant_pid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户PID',
  `merchant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家名称',
  `store_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店id',
  `store_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店名称',
  `coupon_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券ID',
  `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板id',
  `bank_card_cornet` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡的前六后四位',
  `business_district_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商圈名称',
  `business_district_store_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商圈名称',
  `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归属方ID',
  `owner_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归属方名称',
  `config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方ID',
  `config_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方名称',
  `pre_charge_mode` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预充值方式',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appId',
  `funding_pool` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金池ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `refund_time_idx`(`refund_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付宝银行退款账单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_alipay_bank_send_bill
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_alipay_bank_send_bill`;
CREATE TABLE `tb_rc_bill_alipay_bank_send_bill`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `owner_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归属方ID',
  `owner_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '归属方名称',
  `config_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方ID',
  `config_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置方名称',
  `activity_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动ID',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型',
  `send_time` datetime(0) NULL DEFAULT NULL COMMENT '发放时间',
  `face_value` decimal(10, 2) NULL DEFAULT NULL COMMENT '红包面额（元）',
  `coupon_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券ID',
  `send_channel` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发放渠道',
  `available_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '使用开始时间',
  `available_end_time` datetime(0) NULL DEFAULT NULL COMMENT '使用结束时间',
  `template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板id',
  `pre_charge_mode` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预充方式',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appId',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `send_time_idx`(`send_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付宝银行发送账单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_alipay_download_execute_job
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_alipay_download_execute_job`;
CREATE TABLE `tb_rc_bill_alipay_download_execute_job`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '结束时间',
  `month` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '月份',
  `state` tinyint(2) NOT NULL DEFAULT 0 COMMENT '账单下载状态,0-等待下载,1-已提交申请,2-下载成功,3-下载失败,4-时间段内无账单数据',
  `bill_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '账单类型',
  `app_id` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'appId',
  `bill_id` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单Id',
  `bill_data_empty` tinyint(2) NULL DEFAULT NULL COMMENT '账单数据是否为空',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `check_lock` tinyint(2) NULL DEFAULT NULL COMMENT '正在检查账单状态',
  `api_type` tinyint(2) NULL DEFAULT NULL COMMENT '使用接口类型，1-查询活动账单信息,2-日账单创建',
  `activity_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用接口类型为查询活动账单信息时不为空，活动Id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付宝批次下载情况' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_alipay_future_execute_job
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_alipay_future_execute_job`;
CREATE TABLE `tb_rc_bill_alipay_future_execute_job`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `stock_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次Id',
  `alipay_bill_type` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付宝账单类型',
  `used_date_time_range_start` datetime(0) NOT NULL COMMENT '批次任务核销时间开始时间段',
  `used_date_time_range_end` datetime(0) NOT NULL COMMENT '批次任务核销时间段-结束时间',
  `download_state` tinyint(2) NULL DEFAULT NULL COMMENT '下载情况',
  `month` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属月份',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `download_time` datetime(0) NULL DEFAULT NULL COMMENT '下载完成时间',
  `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用ID',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `activity_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动ID',
  `funding_pool` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金池ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `execute_job_stock_id_idx`(`stock_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付宝批次下载情况' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_meituan_bill
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_meituan_bill`;
CREATE TABLE `tb_rc_bill_meituan_bill`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `bill_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单类型',
  `activity_or_batch_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单原始活动ID,支付券批次时表券批次ID,首绑有礼有礼时表批次ID,支付立减时表活动ID',
  `activity_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动ID',
  `activity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `batch_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次ID',
  `coupon_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券码',
  `redeem_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '兑换码',
  `payment_bank_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付银行',
  `distribution_channel` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付通道',
  `transaction_success_time` datetime(0) NULL DEFAULT NULL COMMENT '交易成功时间',
  `order_total_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '支付单金额',
  `discount_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '活动优惠金额',
  `trade_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '银行卡交易金额',
  `refund_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '优惠实际退款金额',
  `payment_bank_card_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡的前六后四位',
  `bank_request_serial_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求银行支付的流水号',
  `bank_response_serial_number` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行返回流水号',
  `bank_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行卡ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '账单详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_meituan_future_execute_job
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_meituan_future_execute_job`;
CREATE TABLE `tb_rc_bill_meituan_future_execute_job`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `stock_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次Id',
  `used_date_time_range_start` datetime(0) NOT NULL COMMENT '批次任务核销时间开始时间段',
  `used_date_time_range_end` datetime(0) NOT NULL COMMENT '批次任务核销时间段-结束时间',
  `job_type` tinyint(2) NOT NULL COMMENT '账单数据来源',
  `download_state` tinyint(2) NULL DEFAULT NULL COMMENT '下载情况',
  `month` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属月份',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `download_time` datetime(0) NULL DEFAULT NULL COMMENT '下载完成时间',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商ID',
  `batch_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tb_rc_bill_meituan_future_execute_job_stock_id_udx`(`stock_id`, `used_date_time_range_start`, `used_date_time_range_end`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '美团批次下载情况' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_stock_future_execute_job
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_stock_future_execute_job`;
CREATE TABLE `tb_rc_bill_stock_future_execute_job`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `stock_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次Id',
  `used_date_time_range_start` datetime(0) NOT NULL COMMENT '批次任务核销时间开始时间段',
  `used_date_time_range_end` datetime(0) NOT NULL COMMENT '批次任务核销时间段-结束时间',
  `job_type` tinyint(2) NOT NULL COMMENT '账单数据来源,0-微信爬虫任务,1-微信活动结束下载任务',
  `download_state` tinyint(2) NULL DEFAULT NULL COMMENT '下载情况,0-未下载,3-已下载',
  `month` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属月份',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `download_time` datetime(0) NULL DEFAULT NULL COMMENT '下载完成时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `execute_job_stock_id_idx`(`stock_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信批次下载情况' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_wechat_bank_bill
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_wechat_bank_bill`;
CREATE TABLE `tb_rc_bill_wechat_bank_bill`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `batch_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次号',
  `discount_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠id',
  `discount_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠类型',
  `discount_amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '优惠金额',
  `order_total_amount` decimal(19, 2) NULL DEFAULT NULL COMMENT '订单总金额',
  `transaction_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易类型',
  `payment_order_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付单号',
  `time` datetime(0) NULL DEFAULT NULL COMMENT '交易成功时间',
  `consumer_business_account` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消费商户号',
  `device_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备号',
  `bank_response_serial_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行支付流水号',
  `goods_detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '单品信息',
  `consumer_store_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消耗门店编码（微信支付）',
  `merchant_consumber_store_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消耗门店编码（商户自有）',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_discount_id_time`(`time`, `discount_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信银行支付账单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_bill_wechat_bank_bill_earliest_time
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_bill_wechat_bank_bill_earliest_time`;
CREATE TABLE `tb_rc_bill_wechat_bank_bill_earliest_time`  (
  `id` bigint(19) NOT NULL AUTO_INCREMENT,
  `discount_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `min_time` datetime(0) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tb_rc_bill_wechat_bank_bill_earliest_time_pk`(`discount_id`) USING BTREE,
  INDEX `discount_id_min_time_idx`(`discount_id`, `min_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 208022 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_card_cipher
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_card_cipher`;
CREATE TABLE `tb_rc_card_cipher`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '码:卡号;卡号+卡密;url短链',
  `denomination` int(11) NOT NULL COMMENT '面值',
  `used` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否使用',
  `expire_time` datetime(0) NULL DEFAULT NULL COMMENT '过期时间',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '使用订单号',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `bar_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡号',
  `bar_pwd` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡密',
  `short_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短链',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商id,不为空时，表明是单独库存',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_sp_type_code`(`sp_type`, `code`) USING BTREE,
  UNIQUE INDEX `uk_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡密' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_card_cipher_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_card_cipher_log`;
CREATE TABLE `tb_rc_card_cipher_log`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `card_cipher_id` bigint(19) NULL DEFAULT NULL COMMENT '卡密主键ID',
  `operate` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作说明',
  `state_change` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态变化',
  `operate_user` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_card_cipher_id`(`card_cipher_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡密操作日志记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_card_cipher_warn
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_card_cipher_warn`;
CREATE TABLE `tb_rc_card_cipher_warn`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `sp_type` tinyint(2) NULL DEFAULT NULL COMMENT '运营商类型',
  `denomination` int(11) NULL DEFAULT NULL COMMENT '面值',
  `warn_value` int(6) NULL DEFAULT NULL COMMENT '提醒阈值',
  `total_quantity` int(11) NULL DEFAULT NULL COMMENT '总数量',
  `unused_quantity` int(11) NULL DEFAULT NULL COMMENT '未使用数量',
  `use_quantity` int(11) NULL DEFAULT NULL COMMENT '已使用量',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uk_sp_type_denomination`(`sp_type`, `denomination`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡密产品库存统计表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channal_appid_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channal_appid_info`;
CREATE TABLE `tb_rc_channal_appid_info`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `app_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app_id',
  `app_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小程序名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_activity_ledger
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_activity_ledger`;
CREATE TABLE `tb_rc_channel_activity_ledger`  (
  `id` bigint(19) NOT NULL COMMENT '主键,记录ID',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '商户id',
  `bank_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行名称',
  `bank_card_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行卡类型',
  `channel_activity_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称,取值充值中心商户商品名称',
  `activity_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型,NORMAL：代金券批次；DISCOUNT_CUT：立减与折扣；OTHER：其他',
  `face_value` decimal(10, 4) NULL DEFAULT NULL COMMENT '充值中心商户商品面值',
  `sku_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'SKU编号,荣数商品编号',
  `proxy_product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值中心商户商品编号',
  `activity_batch_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动关联批次id',
  `activity_start_time` datetime(0) NULL DEFAULT NULL COMMENT '活动开始时间',
  `activity_end_time` datetime(0) NULL DEFAULT NULL COMMENT '活动结束时间',
  `activity_state` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动状态',
  `activity_total_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '活动总预算',
  `distributed_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '活动已发放金额',
  `config_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '已配置预算',
  `use_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '活动已核销金额',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除(0:未删除,1:以删除)',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '荣数活动台账表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_activity_ledger_batch
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_activity_ledger_batch`;
CREATE TABLE `tb_rc_channel_activity_ledger_batch`  (
  `id` bigint(19) NOT NULL COMMENT '主键,记录ID',
  `activity_ledger_id` bigint(19) NULL DEFAULT NULL COMMENT '台账id',
  `activity_batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动关联批次id',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '商户id',
  `proxy_product_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值中心商户商品编号',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除(0:未删除,1:以删除)',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '活动台账和批次关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_ali_activity
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_ali_activity`;
CREATE TABLE `tb_rc_channel_ali_activity`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `supplier_id` bigint(19) NOT NULL COMMENT '供应商id',
  `activity_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动编码',
  `activity_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动名称',
  `activity_type` tinyint(2) NULL DEFAULT NULL COMMENT '活动类型,0-定额立减，1-随机立减，2-首绑有礼，3-通用渠道红包',
  `activity_status` tinyint(2) NULL DEFAULT NULL COMMENT '活动状态',
  `bank_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行名称',
  `use_scene` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销使用场景',
  `begin_time` datetime(0) NULL DEFAULT NULL COMMENT '活动开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '活动结束时间',
  `bank_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行信息',
  `crowd_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '人群信息',
  `activity_statistics_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动统计区信息',
  `activity_time_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动时间信息',
  `budget_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动预算信息',
  `preference_type_info` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠类型信息',
  `count_limit` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计次信息',
  `activity_type_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型信息',
  `the_last_update_time` datetime(0) NULL DEFAULT NULL COMMENT '最近更新活动数据时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除(0:未删除,1:以删除)',
  `today_remaining_count` decimal(10, 2) NULL DEFAULT NULL COMMENT '今日剩余数',
  `today_remaining_budget` decimal(10, 2) NULL DEFAULT NULL COMMENT '今日剩余预算',
  `today_used_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '今日使用预算',
  `yesterday_send_count` bigint(19) NULL DEFAULT NULL COMMENT '截止昨日发送数量',
  `yesterday_send_amount` bigint(19) NULL DEFAULT NULL COMMENT '截止昨日发送预算',
  `yesterday_used_count` bigint(19) NULL DEFAULT NULL COMMENT '截止昨日使用数量',
  `yesterday_used_amount` bigint(19) NULL DEFAULT NULL COMMENT '截止昨日使用预算',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_activity_id`(`activity_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付宝活动配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_car_wash_merchant
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_car_wash_merchant`;
CREATE TABLE `tb_rc_channel_car_wash_merchant`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `merchant_id` bigint(19) NULL DEFAULT NULL COMMENT '外部网点id',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网点名称',
  `short_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '网点简称',
  `position` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '定位类型[tengxun=腾讯; baidu=百度; gaode=高德]',
  `lng` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
  `lat` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `address` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '门店地址',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市',
  `district` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '洗车助手网点表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_mei_tuan_activity
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_mei_tuan_activity`;
CREATE TABLE `tb_rc_channel_mei_tuan_activity`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `batch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '批次号ID，批次id为空，将活动ID存到批次id',
  `activity_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动ID',
  `supplier_id` bigint(19) NOT NULL COMMENT '供应商id',
  `activity_type` tinyint(2) NULL DEFAULT NULL COMMENT '优惠类型;4、收银台·银行卡支付立减；5、银行卡·首绑有礼',
  `batch_status` tinyint(2) NULL DEFAULT NULL COMMENT '批次状态',
  `coupon_type` tinyint(2) NULL DEFAULT NULL COMMENT '券类型',
  `activity_source` tinyint(2) NULL DEFAULT NULL COMMENT '活动来源',
  `discount_detail_dtos` json NULL COMMENT '优惠详情，包含折扣信息',
  `batch_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次名称',
  `title_show` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券展示标题',
  `restrict_show` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券使用限制说明',
  `valid_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '批次有效开始时间',
  `assign_end_time` datetime(0) NULL DEFAULT NULL COMMENT '批次有效结束时间',
  `begin_time` datetime(0) NULL DEFAULT NULL COMMENT '券有效期开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '券有效期结束时间',
  `usable_time_type` tinyint(2) NULL DEFAULT NULL COMMENT '券有效期类型',
  `countdown_days` int(10) NULL DEFAULT NULL COMMENT '倒计时天数',
  `bind_type` tinyint(2) NULL DEFAULT NULL COMMENT '发券形式',
  `receive_limit_dto` json NULL COMMENT '领取限制详情',
  `total_count` int(10) NULL DEFAULT NULL COMMENT '券库存总张数',
  `send_count` int(10) NULL DEFAULT NULL COMMENT '已发张数',
  `used_count` int(10) NULL DEFAULT NULL COMMENT '已用张数',
  `the_last_renew_time` datetime(0) NULL DEFAULT NULL COMMENT '上次更新时间',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `create_by` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除(0:未删除,1:以删除)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '美团活动配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy`;
CREATE TABLE `tb_rc_channel_proxy`  (
  `id` bigint(19) NOT NULL,
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代理商编码',
  `proxy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代理商名称',
  `enable` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用： 0、禁用  1、启用',
  `quota` decimal(16, 4) NULL DEFAULT NULL COMMENT '账户总额',
  `quota_used` decimal(16, 4) NULL DEFAULT NULL COMMENT '账户已用额度',
  `server_ips` varchar(700) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务器地址',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `show_supplier_info` tinyint(2) NULL DEFAULT 0 COMMENT '是否展示供应商信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `send_message_switch` tinyint(2) NULL DEFAULT 0 COMMENT '是否发送短信，0不发送，1发送',
  `warn_contact` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预警联系人',
  `warn_value` decimal(16, 2) NULL DEFAULT NULL COMMENT '预警提醒阈值',
  `balance_way` int(2) NULL DEFAULT 1 COMMENT '结算方式，1预付，2后付',
  `recharge_api_version` tinyint(2) NOT NULL DEFAULT 1 COMMENT 'API版本,1-V1旧版接口,2-V2新版接口',
  `channel_proxy_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '商户类型',
  `wx_activity_settlement_rate` decimal(12, 4) NULL DEFAULT 1.0000 COMMENT '微信活动结算费率',
  `ali_activity_settlement_rate` decimal(12, 4) NULL DEFAULT 1.0000 COMMENT '支付宝活动结算费率',
  `mei_tuan_activity_settlement_rate` decimal(12, 4) NULL DEFAULT 1.0000 COMMENT '美团活动结算费率',
  `wx_activity_settlement` tinyint(2) NULL DEFAULT 1 COMMENT '1(按微信账单结算) ,0(按商户下单结算)',
  `ali_activity_settlement` tinyint(2) NULL DEFAULT 1 COMMENT '1(按支付宝账单结算),0(按商户下单结算)',
  `mei_tuan_activity_settlement` tinyint(2) NULL DEFAULT 1 COMMENT '1(按美团账单结算),0(按商户下单结算)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_code`(`proxy_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理方信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy_abnormal_recharge_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy_abnormal_recharge_record`;
CREATE TABLE `tb_rc_channel_proxy_abnormal_recharge_record`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户订单号',
  `proxy_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户产品编码',
  `supplier_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品id,若未有条码请填AAA',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '产品类型',
  `sp_type` tinyint(2) NULL DEFAULT NULL COMMENT '运营商类型',
  `request_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数信息',
  `exception_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '异常信息',
  `extra_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_proxy_id_product_type_sp_type`(`proxy_code`, `product_type`, `sp_type`) USING BTREE,
  INDEX `idx_proxy_custom_biz_id`(`proxy_custom_biz_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '异常充值记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy_activity
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy_activity`;
CREATE TABLE `tb_rc_channel_proxy_activity`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `proxy_id` bigint(19) NOT NULL COMMENT '商户id',
  `activity_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '活动编码',
  `activity_type` tinyint(2) NULL DEFAULT NULL COMMENT '活动类型',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除(0:未删除,1:以删除)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_activity_type`(`activity_type`) USING BTREE,
  INDEX `idx_activity_id`(`activity_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户活动关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy_balance_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy_balance_log`;
CREATE TABLE `tb_rc_channel_proxy_balance_log`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商id',
  `balance_pre` decimal(16, 4) NULL DEFAULT NULL COMMENT '账户原余额',
  `balance_change` decimal(16, 4) NULL DEFAULT NULL COMMENT '账户变动',
  `balance` decimal(16, 4) NULL DEFAULT NULL COMMENT '账户余额',
  `log_type` tinyint(4) NULL DEFAULT NULL COMMENT '日志类型',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '订单id',
  `user_id` bigint(19) NULL DEFAULT NULL COMMENT '用户id',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_log_type_order`(`log_type`, `order_id`) USING BTREE,
  INDEX `idx_proxy_id`(`proxy_id`) USING BTREE,
  INDEX `idx_proxy_id_log_type`(`proxy_id`, `log_type`) USING BTREE,
  INDEX `idx_proxy_id_log_type_order_id`(`proxy_id`, `log_type`, `order_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理商账户余额变动日志信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy_item
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy_item`;
CREATE TABLE `tb_rc_channel_proxy_item`  (
  `id` bigint(19) NOT NULL,
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商id',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '产品类型(0:话费,1:油卡,2:卡密,3:虚拟卡券)',
  `sp_type` tinyint(2) NULL DEFAULT NULL COMMENT '运营商类型：0移动 1联通 2电信',
  `item_face_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '面值',
  `item_sell_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '销售价',
  `enabled` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用：0、禁用，1、启用',
  `enable` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用：0、禁用，1、启用',
  `product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品id',
  `supplier_product_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品id',
  `support_zone` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支持区域',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `supplier_product_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '供应商商品Id类型',
  `proxy_item_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品名称',
  `send_timing` tinyint(2) NOT NULL DEFAULT 0 COMMENT '商品发送时机',
  `receive_expire_after_create_days` int(10) NULL DEFAULT NULL COMMENT '领取链接有效期按天',
  `receive_expire_specify_date` datetime(0) NULL DEFAULT NULL COMMENT '领取链接有效期按指定日期',
  `receive_trigger_point_type` tinyint(2) NULL DEFAULT NULL COMMENT '领取触发时机',
  `receive_expire_time_type` tinyint(2) NULL DEFAULT NULL COMMENT '领取链接有效期方式',
  `receive_expire_date` datetime(0) NULL DEFAULT NULL COMMENT '领取过期时间',
  `add_budget` tinyint(2) NULL DEFAULT 0 COMMENT '是否启用rpa补充预算',
  `max_budget` decimal(10, 2) NULL DEFAULT NULL COMMENT '总预算上限',
  `add_num` int(8) NULL DEFAULT NULL COMMENT '追加预算个数',
  `product_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '权益可用开始时间',
  `product_end_time` datetime(0) NULL DEFAULT NULL COMMENT '权益可用结束时间',
  `product_express` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '权益说明',
  `product_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品配图',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_proxy_sp_type_face_price`(`proxy_id`, `sp_type`, `item_face_price`) USING BTREE,
  INDEX `idx_product_id`(`proxy_id`, `product_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理方可用面值信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy_item_change_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy_item_change_log`;
CREATE TABLE `tb_rc_channel_proxy_item_change_log`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `pre_value` json NULL COMMENT '修改前数据',
  `new_value` json NULL COMMENT '修改后的数据',
  `operating_account` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作账号',
  `channel_proxy_item_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户商品修改日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy_msg_template
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy_msg_template`;
CREATE TABLE `tb_rc_channel_proxy_msg_template`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '商户id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名称',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '模板内容',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `state` tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '产品类型(0:话费,1:油卡,2:卡密,3:虚拟卡券)',
  `sp_type` tinyint(3) NULL DEFAULT NULL COMMENT '运营商类型',
  `order_state` tinyint(2) NULL DEFAULT NULL COMMENT '订单状态,4充值失败，3充值成功',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户短信模板' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_proxy_order_intercept
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_proxy_order_intercept`;
CREATE TABLE `tb_rc_channel_proxy_order_intercept`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `product_type` tinyint(2) NOT NULL COMMENT '订单产品类型(0:话费,1:油卡)',
  `intercept_label` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '拦截标签(话费:手机号,油卡:卡号)',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_order_intercept_label`(`proxy_id`, `product_type`, `intercept_label`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '代理商订单拦截' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier`;
CREATE TABLE `tb_rc_channel_supplier`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `supplier_type` tinyint(2) NULL DEFAULT NULL COMMENT '供应商类型',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '产品类型(0:话费,1:油卡)',
  `supplier_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商编码',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
  `enable` tinyint(1) NULL DEFAULT NULL COMMENT '是否启用：0、禁用  1、启用',
  `supplier_config` json NULL COMMENT '供应商配置信息',
  `supplier_priority` tinyint(2) NOT NULL DEFAULT 1 COMMENT '供应商优先级',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_supplier_code`(`supplier_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_balance
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_balance`;
CREATE TABLE `tb_rc_channel_supplier_balance`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_type` tinyint(2) NULL DEFAULT NULL COMMENT '供应商类型',
  `account_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号名称',
  `supplier_balance` decimal(12, 2) NULL DEFAULT NULL,
  `balance_threshold` decimal(10, 2) NULL DEFAULT NULL COMMENT '余额阈值',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商余额表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_car_wash_coupon_batch
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_car_wash_coupon_batch`;
CREATE TABLE `tb_rc_channel_supplier_car_wash_coupon_batch`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `out_batch_id` bigint(19) NULL DEFAULT NULL COMMENT '外部批次id',
  `batch_no` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次号',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次标题',
  `category` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `way` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '周期',
  `merchant_id` bigint(19) NULL DEFAULT NULL COMMENT '商户ID',
  `unit_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '单价',
  `original_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `current_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '当前价',
  `effective_days` int(10) NULL DEFAULT NULL COMMENT '有效天数',
  `total` int(10) NULL DEFAULT NULL COMMENT '总额度',
  `send_count` int(10) NULL DEFAULT NULL COMMENT '发行量',
  `used_count` int(10) NULL DEFAULT NULL COMMENT '使用量',
  `gived_count` int(10) NULL DEFAULT NULL COMMENT '转赠数',
  `give_used_count` int(10) NULL DEFAULT NULL COMMENT '转赠用量',
  `is_give` tinyint(2) NULL DEFAULT NULL COMMENT '是否转赠',
  `is_refund` tinyint(2) NULL DEFAULT NULL COMMENT '是否退券',
  `is_sms` tinyint(2) NULL DEFAULT NULL COMMENT '是否通知',
  `is_pay` tinyint(2) NULL DEFAULT NULL COMMENT '是否支付',
  `direction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  `out_updatetime` datetime(0) NULL DEFAULT NULL COMMENT '外部更新日期',
  `out_createtime` datetime(0) NULL DEFAULT NULL COMMENT '外部创建日期',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `out_batch_id_index`(`out_batch_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '洗车券批次表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_direct_recharge_packet
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_direct_recharge_packet`;
CREATE TABLE `tb_rc_channel_supplier_direct_recharge_packet`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '删除状态',
  `packet_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '直充包名称',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商商品直充包' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_direct_recharge_packet_detail
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_direct_recharge_packet_detail`;
CREATE TABLE `tb_rc_channel_supplier_direct_recharge_packet_detail`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `packet_id` bigint(19) NOT NULL COMMENT '直充包ID',
  `supplier_item_id` bigint(19) NOT NULL COMMENT '供应商商品ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商商品直充包明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_item
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_item`;
CREATE TABLE `tb_rc_channel_supplier_item`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品id',
  `supplier_item_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称，供应商内部名称，内部区分商品，不会展示在客户端',
  `item_face_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '面值',
  `item_sell_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '销售价格',
  `enabled` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用：0、禁用，1、启用',
  `support_zone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支持区域',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '产品类型(0:话费,1:油卡,2:卡密,3:虚拟卡券)',
  `sp_type` tinyint(3) NULL DEFAULT NULL COMMENT '运营商类型',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `supplier_item_display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品展示名称，展示在客户端',
  `coupon_type` tinyint(2) NULL DEFAULT NULL COMMENT '卡券类型',
  `supplier_item_backup` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品备用条码',
  `exchange_expire_time` datetime(0) NULL DEFAULT NULL COMMENT '兑换到期时间',
  `exchange_expire_day` int(4) NULL DEFAULT NULL COMMENT '有效兑换天数',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_supplier_sp_type_face_price`(`supplier_id`, `sp_type`, `item_face_price`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商面值信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_item_change_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_item_change_log`;
CREATE TABLE `tb_rc_channel_supplier_item_change_log`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `operating_account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作账户',
  `pre_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改前条码',
  `new_value` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改后的条码',
  `channel_supplier_item_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商面值信息Id',
  `new_item_sell_price_value` decimal(10, 4) NULL DEFAULT NULL COMMENT '新销售价格',
  `pre_item_sell_price_value` decimal(10, 4) NULL DEFAULT NULL COMMENT '修改前销售价格',
  `pre_all_value` json NULL COMMENT '修改前全部数据',
  `new_all_value` json NULL COMMENT '修改后全部的数据',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商商品条码变更记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_item_packet
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_item_packet`;
CREATE TABLE `tb_rc_channel_supplier_item_packet`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '删除状态',
  `packet_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品券包',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商商品券包' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_item_packet_detail
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_item_packet_detail`;
CREATE TABLE `tb_rc_channel_supplier_item_packet_detail`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `supplier_product_packet_id` bigint(19) NOT NULL COMMENT '券包ID',
  `supplier_product_id` bigint(19) NOT NULL COMMENT '供应商商品ID',
  `cycle_order` int(11) NOT NULL COMMENT '周期次序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商商品券包明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_item_wx_info
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_item_wx_info`;
CREATE TABLE `tb_rc_channel_supplier_item_wx_info`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `supplier_item_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品ID',
  `coupon_stocks_status` tinyint(2) NULL DEFAULT NULL COMMENT '微信代金券批次状态',
  `available_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '可用开始时间',
  `available_end_time` datetime(0) NULL DEFAULT NULL COMMENT '可用结束时间',
  `distributed_coupons` int(11) NULL DEFAULT NULL COMMENT '已发券总数',
  `max_coupons` int(11) NULL DEFAULT NULL COMMENT '最大发券数',
  `stock_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次名称',
  `stock_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信批次Id',
  `mch_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商微信微信代金券的信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_supplier_unable_recharge_label
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_supplier_unable_recharge_label`;
CREATE TABLE `tb_rc_channel_supplier_unable_recharge_label`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `supplier_id` bigint(19) NOT NULL COMMENT '供应商id',
  `product_type` tinyint(2) NOT NULL COMMENT '订单产品类型(0:话费,1:油卡)',
  `unable_recharge_label` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '无法充值标签',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_supplier_unable_recharge_label`(`supplier_id`, `product_type`, `unable_recharge_label`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '供应商无法充值标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_wx_activity
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_wx_activity`;
CREATE TABLE `tb_rc_channel_wx_activity`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `batch_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次号',
  `batch_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次名称',
  `mch_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户号',
  `discount_type` tinyint(2) NULL DEFAULT NULL COMMENT '优惠类型;1、随机立减；2，定额立减',
  `stock_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次类型，NORMAL：代金券批次；DISCOUNT_CUT：立减与折扣；OTHER：其他',
  `validity_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '有效期开始时间',
  `validity_end_time` datetime(0) NULL DEFAULT NULL COMMENT '有效期结束时间',
  `to_be_distribute` int(11) NULL DEFAULT NULL COMMENT '剩余数量',
  `distributed` bigint(19) NULL DEFAULT NULL COMMENT '已发数量',
  `total` bigint(19) NULL DEFAULT NULL COMMENT '总量',
  `denomination` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '面额',
  `use_threshold` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用门槛',
  `max_coupons_per_user` int(4) NULL DEFAULT NULL COMMENT '单个用户可领个数',
  `max_amount_by_day` decimal(10, 4) NULL DEFAULT NULL COMMENT '单天发放上限金额',
  `coupon_type` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券类型；满减券，减至券',
  `combine_use` tinyint(2) NULL DEFAULT NULL COMMENT '是否可叠加其他优惠',
  `batch_status` tinyint(2) NULL DEFAULT NULL COMMENT '批次状态，1-未激活，2-审核中，3-运行中，4-已停止，5-暂停发放',
  `notice_threshold` decimal(12, 2) NULL DEFAULT NULL COMMENT '当剩余数量低于配置的阈值，就要发短信提醒手机号;预警百分比',
  `warn_flag` tinyint(2) NULL DEFAULT NULL COMMENT '预警标签,0不用预警，1需要预警',
  `notice_email` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提醒邮箱（多个分号分隔）',
  `notice_phone` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预警提醒手机号（多个分号分隔）',
  `budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '批次预算',
  `refresh_time` datetime(0) NULL DEFAULT NULL COMMENT '刷新时间（请求查询微信批次的时间）',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `notice_threshold_type` tinyint(2) NULL DEFAULT 2 COMMENT '预警阈值类型，1按个数设置，2按百分比设置',
  `notice_threshold_value` int(7) NULL DEFAULT 0 COMMENT '预警个数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信活动配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_channel_wx_activity_backup
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_channel_wx_activity_backup`;
CREATE TABLE `tb_rc_channel_wx_activity_backup`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL COMMENT '删除状态',
  `wx_activity_id` bigint(19) NOT NULL COMMENT '微信活动ID',
  `distributed` bigint(19) NULL DEFAULT NULL COMMENT '批次数量',
  `backup_date` date NOT NULL COMMENT '备份日',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `backup_wx_activity_uk`(`backup_date`, `wx_activity_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_express_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_express_order_detail`;
CREATE TABLE `tb_rc_express_order_detail`  (
  `id` bigint(19) NOT NULL,
  `order_id` bigint(19) NOT NULL COMMENT '快递订单主表ID',
  `time` datetime(0) NOT NULL COMMENT '物流变更时间',
  `logistics_status` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递公司名称',
  `sub_Logistics_status` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物流子状态',
  `logistics_desc` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物流路由信息描述内容',
  `area_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由节点所在地区行政编码',
  `area_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由节点所在地区行政编码',
  `courier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员',
  `courier_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员联系方式',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_id_time_desc`(`order_id`, `time`, `logistics_desc`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递订单明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_express_order_main
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_express_order_main`;
CREATE TABLE `tb_rc_express_order_main`  (
  `id` bigint(19) NOT NULL,
  `express_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递公司编号',
  `express_company_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递公司名称',
  `number` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运单编号',
  `logistics_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '当前最新物流状态码',
  `logistics_status_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '当前最新物流状态描述',
  `the_last_message` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最新的物流信息',
  `the_last_time` datetime(0) NULL DEFAULT NULL COMMENT '最新变更时间',
  `take_time` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '从揽收到送达所耗时间',
  `courier` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员',
  `courier_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递员联系方式',
  `the_last_query_time` datetime(0) NOT NULL COMMENT '最近接口查询时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递订单主表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_express_request_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_express_request_log`;
CREATE TABLE `tb_rc_express_request_log`  (
  `id` bigint(19) NOT NULL,
  `business_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求方唯一编码',
  `express_number` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '快递单号',
  `express_mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递单手机号',
  `express_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '快递公司编码',
  `status` tinyint(2) NULL DEFAULT NULL COMMENT '是否成功',
  `is_call_third` tinyint(2) NULL DEFAULT NULL COMMENT '是否调用第三方',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '订单id',
  `response_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '响应内容',
  `exception_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '异常信息内容',
  `push_time_begin` datetime(0) NULL DEFAULT NULL,
  `push_time_end` datetime(0) NULL DEFAULT NULL,
  `push_time_total` bigint(10) NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '快递查询日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_general_recharge_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_general_recharge_order`;
CREATE TABLE `tb_rc_general_recharge_order`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户名称',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商订单号',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型,3-中石化,4-中石油',
  `account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '充值卡号',
  `account_type` tinyint(2) NULL DEFAULT NULL COMMENT '账户类型',
  `proxy_item_face_price` decimal(8, 2) NOT NULL COMMENT '面值,tb_rc_channel_proxy_item.item_face_price',
  `proxy_item_sell_price` decimal(8, 2) NOT NULL COMMENT '代理商结算价格,tb_rc_channel_proxy_item.item_sell_price',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '持卡人姓名',
  `user_id_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '持卡人身份证',
  `order_state` int(4) NULL DEFAULT NULL COMMENT '状态',
  `proxy_item_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品编码,tb_rc_channel_proxy_item.id',
  `proxy_product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品Id,tb_rc_channel_proxy_item.product_id',
  `callback_msg_creator` tinyint(2) NULL DEFAULT NULL COMMENT '回调内容创建规则',
  `callback_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调地址',
  `receive_message_phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收短信手机号码',
  `include_in_settlement` tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否参与结算，0-不参与结算,1-参与结算',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调商户时间,tb_rc_general_recharge_order_callback',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调时间,tb_rc_general_recharge_order_callback',
  `send_message_state` tinyint(2) NULL DEFAULT NULL COMMENT '发送消息状态',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '成功或失败时间',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '产品类型',
  `supplier_product_type` tinyint(2) NULL DEFAULT NULL COMMENT '供应商商品类型',
  `balance_change` decimal(10, 2) NULL DEFAULT NULL COMMENT '商户变更额度',
  `supplier_product_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品下的tb_rc_channel_proxy_item.supplier_product_id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_order_id`(`proxy_id`, `proxy_custom_biz_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值订单V2表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_general_recharge_order_callback
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_general_recharge_order_callback`;
CREATE TABLE `tb_rc_general_recharge_order_callback`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `general_recharge_order_id` bigint(19) NOT NULL COMMENT '充值订单ID',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调状态',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调内容',
  `callback_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调URL',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商ID',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_callbackstate`(`callback_state`) USING BTREE,
  INDEX `idx_oil_order_id`(`general_recharge_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '油卡回调至第三方商户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_general_recharge_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_general_recharge_order_detail`;
CREATE TABLE `tb_rc_general_recharge_order_detail`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `general_recharge_order_id` bigint(19) NOT NULL COMMENT 'tb_rc_general_recharge_order.id',
  `state` tinyint(2) NOT NULL COMMENT '状态,0-正在处理,1-充值成功,2-充值失败',
  `last_detail_recharge_id` bigint(19) NOT NULL COMMENT '最后一个充值记录的Id，tb_rc_recharge_oil_order_v2_detail_recharge.id',
  `last_supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '最后一个充值明细，供应商id',
  `last_supplier_item_id` bigint(19) NULL DEFAULT NULL COMMENT '最后一个充值明细，供应商商品ID,tb_rc_channel_supplier_item.id',
  `last_supplier_product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后一个充值明细，供应商商品编码，tb_rc_channel_supplier_item.supplier_item_id',
  `last_supplier_item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '最后一个充值明细，供应商商品名称',
  `last_supplier_biz_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单id',
  `last_supplier_item_face_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最后一个充值明细，供应商面值,tb_rc_channel_supplier_item.item_face_price',
  `last_supplier_item_sell_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最后一个充值明细，供应商结算价格,tb_rc_channel_supplier_item.item_sell_price',
  `include_in_settlement` tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否参与结算，0-不参与结算,1-参与结算',
  `last_sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '最后充值明细同步结果时间',
  `entity_card_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实体充值卡卡号',
  `entity_card_cipher` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实体充值卡卡密',
  `supplier_product_type` tinyint(2) NULL DEFAULT NULL COMMENT '充值产品类型',
  `proxy_product_packet_id` bigint(19) NULL DEFAULT NULL COMMENT '充值包ID',
  `last_supplier_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值子订单V2表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_general_recharge_order_detail_recharge
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_general_recharge_order_detail_recharge`;
CREATE TABLE `tb_rc_general_recharge_order_detail_recharge`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `business_order_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '与供应商交互采用的ID,默认与Id一致',
  `general_recharge_order_detail_id` bigint(19) NOT NULL COMMENT 'tb_rc_general_recharge_order_detail.id',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型,3-中石化,4-中石油',
  `account_type` tinyint(2) NOT NULL COMMENT '账户类型',
  `account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '充值卡号',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '持卡人姓名',
  `user_id_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '持卡人身份证',
  `state` tinyint(2) NULL DEFAULT NULL COMMENT '状态,0-已提交,1-推送中,2-充值中,3-充值成功,4-充值失败',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_item_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品ID,tb_rc_channel_supplier_item.supplier_item_id',
  `supplier_product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品编码，tb_rc_channel_supplier_item.supplier_item_id',
  `supplier_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单id',
  `supplier_item_face_price` decimal(8, 2) NULL DEFAULT NULL COMMENT '供应商面值,tb_rc_channel_supplier_item.item_face_price',
  `supplier_item_sell_price` decimal(8, 2) NULL DEFAULT NULL COMMENT '供应商结算价格,tb_rc_channel_supplier_item.item_sell_price',
  `error_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误编码',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商响应错误信息',
  `supplier_item_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品名称',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
  `proxy_product_packet_id` bigint(19) NULL DEFAULT NULL COMMENT '充值包ID',
  `supplier_product_type` tinyint(2) NULL DEFAULT NULL COMMENT '充值产品类型',
  `push_time_begin` datetime(0) NULL DEFAULT NULL COMMENT '推送开始时间',
  `push_time_end` datetime(0) NULL DEFAULT NULL COMMENT '推送结束时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_supplier_biz_id`(`supplier_biz_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值子订单充值记录V2表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_general_recharge_order_detail_recharge_document
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_general_recharge_order_detail_recharge_document`;
CREATE TABLE `tb_rc_general_recharge_order_detail_recharge_document`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `detail_recharge_id` bigint(19) NOT NULL COMMENT 'tb_rc_general_recharge_order_detail_recharge.id',
  `pre_detail_recharge_state` int(4) NULL DEFAULT NULL COMMENT '充值记录变更前的状态',
  `new_detail_recharge_state` int(4) NULL DEFAULT NULL COMMENT '充值记录变更后的状态',
  `proof` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '证明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡券流水' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_kafka_error_msg
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_kafka_error_msg`;
CREATE TABLE `tb_rc_kafka_error_msg`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '删除状态',
  `topic` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'kafka执行异常记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_lazy_job_10
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_lazy_job_10`;
CREATE TABLE `tb_rc_lazy_job_10`  (
  `id` bigint(19) NOT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(4) NULL DEFAULT NULL COMMENT '删除状态',
  `worker` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调度方法',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `util` datetime(0) NULL DEFAULT NULL COMMENT '直到',
  `lazy_job_lazy_level` int(6) NULL DEFAULT NULL COMMENT '延迟级别',
  `take_state` tinyint(2) NOT NULL DEFAULT 0 COMMENT '状态,0-未取走，1-已被取走',
  `take_time` datetime(0) NULL DEFAULT NULL COMMENT '取走时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tb_rc_lazy_job_10_pk`(`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_lazy_job_dead_letter
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_lazy_job_dead_letter`;
CREATE TABLE `tb_rc_lazy_job_dead_letter`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(4) NULL DEFAULT NULL COMMENT '删除状态',
  `worker` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '调度方法',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `util` datetime(0) NULL DEFAULT NULL COMMENT '直到',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_mock_supplier_response
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_mock_supplier_response`;
CREATE TABLE `tb_rc_mock_supplier_response`  (
  `id` bigint(19) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `recharge_response_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '下单返回内容',
  `query_response_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '查询返回内容',
  `api_id` bigint(19) NOT NULL COMMENT '与供应商交互的ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模拟供应商响应内容' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_month_proxy_settlement_data
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_month_proxy_settlement_data`;
CREATE TABLE `tb_rc_month_proxy_settlement_data`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `settlement_month` date NOT NULL COMMENT '结算月份',
  `proxy_id` bigint(19) NOT NULL COMMENT '商户id',
  `proxy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户名称',
  `settlement_product_type` tinyint(3) NOT NULL COMMENT '结算产品类型,1（话费）；2（油卡）；3（卡密结算）；4（直充权益）；5（微信）；6（支付宝）；7（美团）',
  `settlement_type` tinyint(3) NOT NULL COMMENT '结算方式,1(按商户下单结算);2(按微信账单结算);3(按支付宝账单结算);4(按美团账单结算)',
  `order_num` int(11) NULL DEFAULT 0 COMMENT '订单数量',
  `order_pay_num` int(11) NULL DEFAULT 0 COMMENT '支付笔数',
  `order_back_num` int(11) NULL DEFAULT 0 COMMENT '退回笔数',
  `order_pay_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '支付金额',
  `order_back_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '退回金额',
  `receive_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '应收金额(元)',
  `cost_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '成本金额(元)',
  `refund_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '商户退货金额(元)',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `profit` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '利润',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_settlement_month_proxy_id`(`settlement_month`, `proxy_id`, `settlement_product_type`, `settlement_type`) USING BTREE,
  INDEX `idx_settlement_month`(`settlement_month`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '月度商户结算数据汇总表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_month_proxy_settlement_detail
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_month_proxy_settlement_detail`;
CREATE TABLE `tb_rc_month_proxy_settlement_detail`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `proxy_settlement_id` bigint(19) NOT NULL COMMENT '月度商户结算数据表id',
  `supplier_id` bigint(19) NOT NULL COMMENT '供应商id',
  `sp_type` int(11) NOT NULL COMMENT '运营商类型',
  `order_num` int(11) NULL DEFAULT 0 COMMENT '订单数量(笔)',
  `ali_activity_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型(支付宝)',
  `mei_tuan_bill_type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单类型(美团)',
  `wx_discount_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠类型(微信)',
  `order_pay_num` int(11) NULL DEFAULT 0 COMMENT '支付笔数',
  `order_back_num` int(11) NULL DEFAULT 0 COMMENT '退回笔数',
  `order_pay_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '支付金额',
  `order_back_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '退回金额',
  `receive_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '应收金额(元)',
  `cost_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '成本金额(元)',
  `refund_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '商户退货金额(元)',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `profit` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '利润',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '月度商户结算数据明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_month_recharge_order_summary_cache
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_month_recharge_order_summary_cache`;
CREATE TABLE `tb_rc_month_recharge_order_summary_cache`  (
  `id` bigint(19) NOT NULL,
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商ID',
  `supplier_id` bigint(20) NULL DEFAULT NULL COMMENT '供应商ID',
  `sp_type` int(11) NULL DEFAULT NULL COMMENT '运营商类型',
  `count` bigint(19) NULL DEFAULT NULL COMMENT '商户订单数据',
  `total_sell_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '商户结算金额',
  `month` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '月份',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_month_supplier_settlement_data
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_month_supplier_settlement_data`;
CREATE TABLE `tb_rc_month_supplier_settlement_data`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `settlement_month` date NOT NULL COMMENT '结算月份',
  `supplier_id` bigint(19) NOT NULL COMMENT '供应商id',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供应商名称',
  `settlement_product_type` tinyint(3) NOT NULL COMMENT '结算产品类型,1（话费）；2（油卡）；3（卡密结算）；4（直充权益）；5（微信）；6（支付宝）；7（美团）',
  `settlement_type` tinyint(3) NOT NULL COMMENT '结算方式,1(按供应商下单结算);2(按微信账单结算);3(按支付宝账单结算);4(按美团账单结算)',
  `order_num` int(11) NULL DEFAULT 0 COMMENT '订单数量',
  `receive_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '应收金额(元)',
  `cost_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '成本金额(元)',
  `order_pay_num` int(11) NULL DEFAULT 0 COMMENT '支付笔数',
  `order_back_num` int(11) NULL DEFAULT 0 COMMENT '退回笔数',
  `order_pay_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '支付金额',
  `order_back_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '退回金额',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `profit` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '利润',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_settlement_month_supplier_id`(`settlement_month`, `supplier_id`, `settlement_product_type`, `settlement_type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '月度供应商结算数据汇总表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_month_supplier_settlement_detail
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_month_supplier_settlement_detail`;
CREATE TABLE `tb_rc_month_supplier_settlement_detail`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `supplier_settlement_id` bigint(19) NOT NULL COMMENT '月度商户结算数据表id',
  `proxy_id` bigint(19) NOT NULL COMMENT '商户id',
  `proxy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户名称',
  `sp_type` int(11) NOT NULL COMMENT '运营商类型',
  `order_num` int(11) NULL DEFAULT 0 COMMENT '订单数量(笔)',
  `receive_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '应收金额(元)',
  `cost_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '成本金额(元)',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `profit` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '利润',
  `order_pay_num` int(11) NULL DEFAULT 0 COMMENT '支付笔数',
  `order_back_num` int(11) NULL DEFAULT 0 COMMENT '退回笔数',
  `order_pay_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '支付金额',
  `order_back_amount` decimal(12, 2) NULL DEFAULT 0.00 COMMENT '退回金额',
  `ali_activity_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '活动类型(支付宝)',
  `mei_tuan_bill_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账单类型(美团)',
  `wx_discount_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠类型(微信)',
  `detail_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '明细类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '月度商户结算数据明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_name
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_name`;
CREATE TABLE `tb_rc_name`  (
  `id` bigint(20) NOT NULL,
  `column_name` varchar(23) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_oil_entity_card
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_oil_entity_card`;
CREATE TABLE `tb_rc_oil_entity_card`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `card_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '卡号',
  `card_cipher` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '卡密',
  `denomination` int(11) NOT NULL COMMENT '面值',
  `state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态(0未使用1使用中2已使用)',
  `expire_time` datetime(0) NULL DEFAULT NULL COMMENT '过期时间',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '使用订单号',
  `recharge_info` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_card_no_cipher`(`card_no`, `card_cipher`) USING BTREE,
  UNIQUE INDEX `uk_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '油卡充值实体卡密' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_oil_secondary_card
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_oil_secondary_card`;
CREATE TABLE `tb_rc_oil_secondary_card`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `card_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '卡号',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_card_no`(`card_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '油卡副卡信息收集表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_proxy_balance_statistic
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_proxy_balance_statistic`;
CREATE TABLE `tb_rc_proxy_balance_statistic`  (
  `id` bigint(19) NOT NULL COMMENT 'ID',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '商户id',
  `proxy_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户编码',
  `proxy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户名称',
  `quota` decimal(16, 4) NULL DEFAULT NULL COMMENT '总额度',
  `quota_used_today` decimal(16, 4) NULL DEFAULT NULL COMMENT '当日消耗额度',
  `quota_available` decimal(16, 4) NULL DEFAULT NULL COMMENT '当日剩余额度',
  `recharge_amount` decimal(16, 4) NULL DEFAULT NULL COMMENT '当日充值额度',
  `order_success_count` int(8) NULL DEFAULT NULL COMMENT '成功订单量',
  `order_failed_count` int(8) NULL DEFAULT NULL COMMENT '失败订单量',
  `order_unfinished_count` int(8) NULL DEFAULT NULL COMMENT '未完成订单量',
  `order_unfinished_amount` decimal(16, 4) NULL DEFAULT NULL COMMENT '未完成订单占用总额',
  `order_total` int(8) NULL DEFAULT NULL COMMENT '总订单量',
  `statistic_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计日期',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_proxy_id`(`proxy_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户额度每日汇总信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_proxy_item_card_cipher
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_proxy_item_card_cipher`;
CREATE TABLE `tb_rc_proxy_item_card_cipher`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `card_cipher_warn_id` bigint(19) NOT NULL COMMENT '卡密库存预警表id',
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商id',
  `proxy_item_id` bigint(19) NOT NULL COMMENT '商户商品id',
  `assign_type` tinyint(2) NULL DEFAULT NULL COMMENT '分配模式，1、公用库存，2、单独分配',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_proxy_item_id`(`proxy_item_id`) USING BTREE,
  INDEX `idx_card_cipher_warn_id`(`card_cipher_warn_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户卡密商品库存管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_card_cipher_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_card_cipher_order`;
CREATE TABLE `tb_rc_recharge_card_cipher_order`  (
  `id` bigint(19) NOT NULL,
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商订单号',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `receive_phone_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收手机号',
  `item_face_price` decimal(8, 2) NOT NULL COMMENT '面值',
  `proxy_item_sell_price` decimal(8, 2) NOT NULL COMMENT '代理商结算价格',
  `order_state` tinyint(2) NULL DEFAULT NULL COMMENT '状态',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品编码',
  `supplier_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单id',
  `supplier_item_sell_price` decimal(8, 2) NULL DEFAULT NULL COMMENT '供应商结算价格',
  `push_time_begin` datetime(0) NULL DEFAULT NULL COMMENT '推送开始时间',
  `push_time_end` datetime(0) NULL DEFAULT NULL COMMENT '推送结束时间',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `sp_ticket` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营商凭证',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调状态',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  `callback_times` tinyint(2) NULL DEFAULT NULL COMMENT '回调次数',
  `callback_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调地址',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `send_message_state` tinyint(2) NULL DEFAULT 4 COMMENT '发送短信状态，1发送中，2发送成功，3发送失败，4无需发送',
  `error_msg` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '错误信息',
  `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口返回状态码',
  `proxy_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品编码',
  `proxy_product_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品Id,tb_rc_channel_proxy_item.id',
  `business_order_id` bigint(19) NULL DEFAULT NULL COMMENT '业务订单号',
  `exchange_expire_time` datetime(0) NULL DEFAULT NULL COMMENT '兑换到期时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_order_id`(`proxy_id`, `proxy_custom_biz_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_business_order_id`(`business_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡密充值订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_compensation_batch
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_compensation_batch`;
CREATE TABLE `tb_rc_recharge_compensation_batch`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `product_type` tinyint(2) NOT NULL COMMENT '产品类型(0:话费,1:油卡,2:卡密,3:虚拟卡券)',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型：0移动 1联通 2电信',
  `item_face_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '面值',
  `account_type` tinyint(2) NOT NULL COMMENT '充值账号类型',
  `wechat_app_id` bigint(19) NULL DEFAULT NULL COMMENT '微信公众号ID',
  `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appid',
  `number` int(11) NOT NULL COMMENT '创建数量',
  `state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
  `apply_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补偿原因',
  `create_by` bigint(19) NOT NULL COMMENT '申请人',
  `proxy_id` bigint(19) NOT NULL COMMENT '商户id',
  `proxy_item_id` bigint(19) NOT NULL COMMENT '商户商品ID',
  `proxy_product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品条码',
  `proxy_item_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品名称',
  `supplier_item_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品ID',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品条码',
  `supplier_product_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '供应商商品Id类型',
  `audit_state` tinyint(2) NOT NULL COMMENT '审核状态',
  `audit_time` datetime(0) NULL DEFAULT NULL COMMENT '审核时间',
  `audit_by` bigint(19) NULL DEFAULT NULL COMMENT '审核人Id',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核意见',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '删除状态',
  `fail_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败原因',
  `real_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核人ip地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '补偿批次' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_compensation_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_compensation_record`;
CREATE TABLE `tb_rc_recharge_compensation_record`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(11) NULL DEFAULT 0 COMMENT '删除状态',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '订单ID',
  `compensation_batch_id` bigint(19) NOT NULL COMMENT '批次ID,tb_rc_recharge_compensation_batch.id',
  `state` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '充值账号',
  `custom_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自定义订单号',
  `receive_message_phone_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收短信手机号码',
  `fail_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '失败原因',
  `generate_time` datetime(0) NULL DEFAULT NULL COMMENT '下单时间',
  `old_proxy_order_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客诉的旧商户订单号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_id`(`order_id`) USING BTREE,
  INDEX `index_compensation_batch_id`(`compensation_batch_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '补偿批次详情明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_coupon
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_coupon`;
CREATE TABLE `tb_rc_recharge_coupon`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `user_id` bigint(19) NULL DEFAULT NULL COMMENT '归属用户ID',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '删除状态',
  `account_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值账号',
  `coupon_order_id` bigint(19) NOT NULL COMMENT '卡券订单ID',
  `state` int(4) NOT NULL DEFAULT 0 COMMENT '状态,100-未领取,101-预计发放,102-待发放,103-发放中,104-发送失败,105-发送失败(备用批次补发),106-发送失败,200-已发券,300-已使用,400-已过期,500-已拦截,501-已取消',
  `expected_send_time` datetime(0) NULL DEFAULT NULL COMMENT '预期发送时间,默认为券创建时间',
  `send_time` datetime(0) NULL DEFAULT NULL COMMENT '发送时间',
  `available_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '可用开始时间，默认为发送时间',
  `available_end_time` datetime(0) NULL DEFAULT NULL COMMENT '过期时间',
  `used_time` datetime(0) NULL DEFAULT NULL COMMENT '使用时间',
  `coupon_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券名称',
  `amount` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '卡券的面值',
  `coupon_type` tinyint(2) NULL DEFAULT NULL COMMENT '卡券类型',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT ' 代理商ID',
  `supplier_coupon_no` varchar(127) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '第三方券码，供应商卡券ID',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商ID',
  `proxy_product_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商商品ID',
  `supplier_product_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品ID',
  `proxy_product_sell_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '商户结算价格',
  `supplier_product_sell_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '供应商结算价',
  `proxy_product_packet_id` bigint(19) NULL DEFAULT NULL COMMENT '券包Id',
  `charge_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值编码',
  `supplier_biz_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单号',
  `error_msg` tinytext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '错误信息',
  `account_type` tinyint(2) NULL DEFAULT NULL COMMENT '账户类型,0-其他,1-手机号,2-微信openId,3-邮箱,4-支付宝用户openId,5-QQ号,6-航空公司会员,7-游戏账号,8-油卡',
  `app_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appid',
  `proxy_product_packet_detail_id` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'tb_rc_channel_supplier_item_packet_item.id,券包明细Id',
  `send_message_state` tinyint(2) NULL DEFAULT 4 COMMENT '发送短信状态，1发送中，2发送成功，3发送失败，4无需发送',
  `receive_message_phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收短信手机号码',
  `include_in_settlement` tinyint(3) NULL DEFAULT 1 COMMENT '是否参与与供应商的结算',
  `supplier_custom_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡券自定义信息域',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tb_rc_recharge_coupon_supplier_coupon_no_index`(`supplier_coupon_no`) USING BTREE,
  INDEX `idx_coupon_order_id`(`coupon_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡券' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_coupon_account_change_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_coupon_account_change_record`;
CREATE TABLE `tb_rc_recharge_coupon_account_change_record`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `before_coupon_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变更前的卡券信息',
  `after_coupon_info` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变更后的卡券信息',
  `coupon_id` bigint(19) NOT NULL COMMENT '券ID',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '删除状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡券到账账户变更信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_coupon_callback
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_coupon_callback`;
CREATE TABLE `tb_rc_recharge_coupon_callback`  (
  `id` bigint(19) NULL DEFAULT NULL COMMENT '主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '删除状态',
  `event_type` int(4) NULL DEFAULT NULL COMMENT '回调类型',
  `coupon_id` bigint(19) NULL DEFAULT NULL COMMENT '卡券ID',
  `callback_state` int(4) NULL DEFAULT NULL COMMENT '回调状态,0-等待回调,1-回调中,2-回调成功,3-回调失败',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调内容',
  `callback_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调URL',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商ID',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  INDEX `tb_rc_recharge_coupon_callback_coupon_id_index`(`coupon_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 32117 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡券回调至第三方商户' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_coupon_code
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_coupon_code`;
CREATE TABLE `tb_rc_recharge_coupon_code`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `coupon_id` bigint(19) NOT NULL COMMENT '发券记录id',
  `bar_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡号',
  `bar_pwd` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '卡密',
  `short_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短链',
  `code_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券码唯一标识',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `card_expire_time` datetime(0) NULL DEFAULT NULL COMMENT '卡密过期时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '虚拟卡券订单券码信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_coupon_document
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_coupon_document`;
CREATE TABLE `tb_rc_recharge_coupon_document`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `coupon_id` bigint(19) NOT NULL COMMENT '券id',
  `pre_coupon_state` int(4) NULL DEFAULT NULL COMMENT '卡券的当前状态',
  `new_coupon_state` int(4) NULL DEFAULT NULL COMMENT '卡券的当前状态',
  `amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '流水涉及到的金额',
  `document_type` tinyint(2) NOT NULL COMMENT '流水记录的操作类型，如领取、使用、过期',
  `document_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流水号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡券流水' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_coupon_expire_event
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_coupon_expire_event`;
CREATE TABLE `tb_rc_recharge_coupon_expire_event`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `coupon_id` bigint(19) NULL DEFAULT NULL COMMENT '券Id',
  `available_end_time` datetime(0) NOT NULL COMMENT '过期时间',
  `event_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '事件状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `coupon_id_uindex`(`coupon_id`) USING BTREE,
  INDEX `event_available_end_time_idx`(`available_end_time`, `event_status`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '卡券过期事件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_coupon_supplier_request_id_store
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_coupon_supplier_request_id_store`;
CREATE TABLE `tb_rc_recharge_coupon_supplier_request_id_store`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `coupon_id` bigint(19) NOT NULL COMMENT '券ID',
  `supplier_request_id` bigint(19) NOT NULL COMMENT '请求第三方商户使用的id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '请求第三方供应商的券' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_integral_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_integral_order`;
CREATE TABLE `tb_rc_recharge_integral_order`  (
  `id` bigint(19) NOT NULL,
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商订单号',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `item_face_price` int(10) NOT NULL COMMENT '面值,积分充值数量',
  `proxy_item_sell_price` decimal(8, 2) NULL DEFAULT NULL COMMENT '代理商结算价格',
  `user_name` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `user_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id，南航会员卡号',
  `phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话号码',
  `order_state` tinyint(2) NULL DEFAULT NULL COMMENT '订单状态',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品编码',
  `supplier_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单id',
  `supplier_item_sell_price` decimal(8, 2) NULL DEFAULT NULL COMMENT '供应商结算价格',
  `push_time_begin` datetime(0) NULL DEFAULT NULL COMMENT '推送开始时间',
  `push_time_end` datetime(0) NULL DEFAULT NULL COMMENT '推送结束时间',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `sp_ticket` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营商凭证',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调状态',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  `callback_times` tinyint(2) NULL DEFAULT NULL COMMENT '回调次数',
  `callback_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调地址',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `supplier_trans_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家交易流水号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_order_id`(`proxy_id`, `proxy_custom_biz_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_phone_no`(`phone_no`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '南航里程积分兑换记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_oil_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_oil_order`;
CREATE TABLE `tb_rc_recharge_oil_order`  (
  `id` bigint(19) NOT NULL,
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商订单号',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `card_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '充值卡号',
  `item_face_price` decimal(8, 2) NOT NULL COMMENT '面值',
  `proxy_item_sell_price` decimal(8, 2) NOT NULL COMMENT '代理商结算价格',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '持卡人姓名',
  `user_id_card` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '持卡人身份证',
  `order_state` tinyint(2) NULL DEFAULT NULL COMMENT '状态',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品编码',
  `supplier_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单id',
  `supplier_item_sell_price` decimal(8, 2) NULL DEFAULT NULL COMMENT '供应商结算价格',
  `push_time_begin` datetime(0) NULL DEFAULT NULL COMMENT '推送开始时间',
  `push_time_end` datetime(0) NULL DEFAULT NULL COMMENT '推送结束时间',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `sp_ticket` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营商凭证',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调状态',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  `callback_times` tinyint(2) NULL DEFAULT NULL COMMENT '回调次数',
  `callback_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调地址',
  `entity_card_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实体充值卡卡号',
  `entity_card_cipher` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实体充值卡卡密',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `send_message_state` tinyint(2) NULL DEFAULT 4 COMMENT '发送短信状态，1发送中，2发送成功，3发送失败，4无需发送',
  `receive_message_phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收短信手机号码',
  `proxy_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品编码',
  `proxy_product_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品Id,tb_rc_channel_proxy_item.id',
  `supplier_product_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品ID,tb_rc_channel_supplier_item.id',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口返回错误码，用于保存失败状态码',
  `business_order_id` bigint(19) NULL DEFAULT NULL COMMENT '业务订单号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_order_id`(`proxy_id`, `proxy_custom_biz_id`) USING BTREE,
  INDEX `idx_supplier_order_id`(`supplier_id`, `supplier_biz_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_card_no`(`card_no`) USING BTREE,
  INDEX `idx_business_order_id`(`business_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '油卡充值订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_order_callback_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_order_callback_log`;
CREATE TABLE `tb_rc_recharge_order_callback_log`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调重播内容',
  `callback_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '商户id',
  `order_callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '订单回调状态',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '订单类型',
  `plain_text` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调明文',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '订单ID',
  `proxy_product_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单回调记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_order_expire_event
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_order_expire_event`;
CREATE TABLE `tb_rc_recharge_order_expire_event`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '订单ID',
  `available_end_time` datetime(0) NOT NULL COMMENT '过期时间',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '订单类型',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id_uindex`(`order_id`) USING BTREE,
  INDEX `tb_rc_recharge_order_expire_event_available_end_time_index`(`available_end_time`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单过期事件' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_order_info_change
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_order_info_change`;
CREATE TABLE `tb_rc_recharge_order_info_change`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `order_id` bigint(19) NOT NULL COMMENT '订单表主键id',
  `business_order_id` bigint(19) NOT NULL COMMENT '新业务订单号',
  `old_business_order_id` bigint(19) NOT NULL COMMENT '旧充值订单号',
  `account_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '新充值账号(油卡号或手机号)',
  `old_account_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '旧充值账号(油卡号或手机号)',
  `product_type` tinyint(2) NOT NULL COMMENT '产品类型(0:话费,1:油卡,2:卡密,3:虚拟卡券)',
  `request_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数信息',
  `extra_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展信息',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id_old_business_order_id`(`order_id`, `old_business_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单信息变更记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_order_main
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_order_main`;
CREATE TABLE `tb_rc_recharge_order_main`  (
  `id` bigint(19) NOT NULL,
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '代理商id',
  `proxy_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商自定义流水号',
  `phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '电话号码',
  `sp_type` tinyint(2) NULL DEFAULT NULL COMMENT '运营商类型',
  `item_face_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '面值',
  `proxy_item_sell_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '代理商结算价',
  `supplier_item_sell_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '供应商结算价',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品编码',
  `supplier_biz_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商业务流水号',
  `callback_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调地址',
  `order_state` tinyint(3) NULL DEFAULT NULL COMMENT '订单状态',
  `push_time_begin` datetime(0) NULL DEFAULT NULL COMMENT '推送开始时间',
  `push_time_end` datetime(0) NULL DEFAULT NULL COMMENT '推送结束时间',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `push_time_total` int(8) NULL DEFAULT NULL COMMENT '推送耗时/ms',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调状态',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  `sp_ticket` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营商凭证',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `send_message_state` tinyint(2) NULL DEFAULT 4 COMMENT '发送短信状态，1发送中，2发送成功，3发送失败，4无需发送',
  `receive_message_phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收短信手机号码',
  `proxy_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品编码',
  `proxy_product_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品Id',
  `supplier_product_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品ID',
  `supplier_intercept` tinyint(2) NULL DEFAULT 0 COMMENT '上游拦截标识',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口返回错误码，用于保存失败状态码',
  `business_order_id` bigint(19) NULL DEFAULT NULL COMMENT '业务订单号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_custom_biz_id`(`proxy_code`, `proxy_custom_biz_id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_id_custom_biz_id`(`proxy_id`, `proxy_custom_biz_id`) USING BTREE,
  INDEX `idx_order_state`(`order_state`) USING BTREE,
  INDEX `idx_callback_state`(`callback_state`) USING BTREE,
  INDEX `idx_phone_no`(`phone_no`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_business_order_id`(`business_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值订单信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_order_statistic
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_order_statistic`;
CREATE TABLE `tb_rc_recharge_order_statistic`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '商户id',
  `proxy_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户编码',
  `proxy_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户名称',
  `sp_type` tinyint(2) NULL DEFAULT NULL COMMENT '运营商类型',
  `item_face_price` decimal(16, 4) NULL DEFAULT NULL COMMENT '面值',
  `proxy_item_sale_price` decimal(16, 4) NULL DEFAULT NULL COMMENT '销售价格',
  `proxy_discount_rate` decimal(8, 4) NULL DEFAULT NULL COMMENT '售价折扣率',
  `proxy_discount` decimal(16, 4) NULL DEFAULT NULL COMMENT '售价折扣额',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商名称',
  `supplier_item_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品ID',
  `support_zone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '销售地区',
  `supplier_item_sale_price` decimal(16, 4) NULL DEFAULT NULL COMMENT '采购成本价',
  `supplier_item_discount_rate` decimal(8, 4) NULL DEFAULT NULL COMMENT '采购成本折扣率',
  `tax_rate` decimal(8, 4) NULL DEFAULT NULL COMMENT '税率',
  `order_success_count` int(8) NULL DEFAULT NULL COMMENT '充值成功订单数',
  `sale` decimal(16, 4) NULL DEFAULT NULL COMMENT '销售额',
  `item_face_sale` decimal(16, 4) NULL DEFAULT NULL COMMENT '销售总面值',
  `purchase_amount` decimal(16, 4) NULL DEFAULT NULL COMMENT '采购成本总额',
  `discount_amount` decimal(16, 4) NULL DEFAULT NULL COMMENT '折扣总额',
  `invoice_tax_point_cost` decimal(10, 4) NULL DEFAULT NULL COMMENT '发票税点费率',
  `statistic_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计日期',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_proxy_id`(`proxy_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户订单统计汇总信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_order_statistic_exception
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_order_statistic_exception`;
CREATE TABLE `tb_rc_recharge_order_statistic_exception`  (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `statistic_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统计日期',
  `unfinished_count` int(8) NULL DEFAULT NULL COMMENT '未完成订单量',
  `unfinished_amount` decimal(16, 4) NULL DEFAULT NULL COMMENT '未完成订单额度',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_statistic_date`(`statistic_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '未完成订单统计信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_virtual_coupon_activate_batch
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_virtual_coupon_activate_batch`;
CREATE TABLE `tb_rc_recharge_virtual_coupon_activate_batch`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `create_by` bigint(19) NOT NULL COMMENT '创建人',
  `proxy_item_id` bigint(19) NOT NULL COMMENT '代理商商品ID',
  `number` int(11) NOT NULL COMMENT '创建数量',
  `generate_receive_type` tinyint(1) NULL DEFAULT 0 COMMENT '生成领取方式',
  `receive_expire_date` datetime(0) NULL DEFAULT NULL COMMENT '领取过期时间',
  `state` tinyint(4) NULL DEFAULT 0 COMMENT '状态',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '领取链接批次' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_virtual_coupon_activate_batch_details
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_virtual_coupon_activate_batch_details`;
CREATE TABLE `tb_rc_recharge_virtual_coupon_activate_batch_details`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` int(11) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `order_id` bigint(19) NOT NULL COMMENT '激活批次创建的订单ID',
  `activate_batch_id` bigint(19) NOT NULL COMMENT '补偿批次ID,tb_rc_recharge_virtual_coupon_activate_batch.id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_activate_batch_id_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '补偿批次明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_virtual_coupon_activate_batch_style
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_virtual_coupon_activate_batch_style`;
CREATE TABLE `tb_rc_recharge_virtual_coupon_activate_batch_style`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `background_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '背景图片',
  `activate_batch_id` bigint(19) NOT NULL COMMENT '领取链接批次,tb_rc_recharge_virtual_coupon_activate_batch.id',
  `activity_rule` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '活动规则',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '领取批次样式' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_virtual_coupon_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_virtual_coupon_order`;
CREATE TABLE `tb_rc_recharge_virtual_coupon_order`  (
  `id` bigint(19) NOT NULL,
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商订单号',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `account_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '充值账号',
  `receive_phone_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收手机号',
  `item_face_price` decimal(12, 4) NOT NULL COMMENT '面值',
  `proxy_item_sell_price` decimal(12, 4) NOT NULL COMMENT '代理商结算价格',
  `order_state` tinyint(2) NULL DEFAULT NULL COMMENT '状态',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `supplier_item_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品编码',
  `supplier_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单id',
  `supplier_item_sell_price` decimal(8, 2) NULL DEFAULT NULL COMMENT '供应商结算价格',
  `push_time_begin` datetime(0) NULL DEFAULT NULL COMMENT '推送开始时间',
  `push_time_end` datetime(0) NULL DEFAULT NULL COMMENT '推送结束时间',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `sp_ticket` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '运营商凭证',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调状态',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  `callback_times` tinyint(2) NULL DEFAULT NULL COMMENT '回调次数',
  `callback_url` varchar(380) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调地址',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `supplier_product_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '供应商商品编码类型',
  `proxy_product_id` bigint(19) NOT NULL COMMENT '商户下单产品',
  `user_id` bigint(19) NULL DEFAULT NULL COMMENT '用户ID',
  `proxy_item_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品名称',
  `product_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品编码',
  `coupon_activate_url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '激活短链',
  `account_type` tinyint(2) NULL DEFAULT NULL COMMENT '充值账号类型',
  `account_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '充值账号附加信息',
  `receive_trigger_point_type` tinyint(2) NULL DEFAULT NULL COMMENT '领取时机',
  `receive_expire_date` datetime(0) NULL DEFAULT NULL COMMENT '领取有效期',
  `callback_msg_creator` tinyint(2) NULL DEFAULT NULL COMMENT '回调内容创建规则',
  `proxy_custom_info` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户自定义信息',
  `include_in_settlement` tinyint(2) NOT NULL DEFAULT 1 COMMENT '是否参与结算',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_order_id`(`proxy_id`, `proxy_custom_biz_id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE,
  INDEX `idx_account_no`(`account_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '虚拟卡券充值订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_virtual_coupon_order_receive_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_virtual_coupon_order_receive_record`;
CREATE TABLE `tb_rc_recharge_virtual_coupon_order_receive_record`  (
  `id` bigint(19) NOT NULL COMMENT '创建时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '删除状态',
  `coupon_order_id` bigint(19) NOT NULL COMMENT '卡券订单ID',
  `receive_time` datetime(0) NOT NULL COMMENT '领取时间',
  `account_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '领取账号',
  `order_receive_account_type` tinyint(2) NOT NULL COMMENT '订单领取账户类型',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_coupon_order_id`(`coupon_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '虚拟卡券领取记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_recharge_xing_can_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_recharge_xing_can_order`;
CREATE TABLE `tb_rc_recharge_xing_can_order`  (
  `id` bigint(19) NOT NULL COMMENT 'ID',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `business_order_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务订单号',
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商订单号',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `face_price` decimal(10, 2) NOT NULL COMMENT '面值',
  `supplier_settlement_price` decimal(10, 2) NOT NULL COMMENT '供应商结算价',
  `proxy_settlement_price` decimal(10, 2) NOT NULL COMMENT '商户结算价',
  `link_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务详情H5 url',
  `app_id` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'AppId',
  `applet_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务详情小程序path路径',
  `supplier_item_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商商品Id',
  `supplier_product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商商品条码',
  `supplier_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '供应商订单号',
  `coupon_state` int(4) NULL DEFAULT NULL COMMENT '券状态',
  `send_time` datetime(0) NULL DEFAULT NULL COMMENT '发送时间',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `ex_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `sg_order_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务商品订单号',
  `service_goods_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务商品编码',
  `service_goods_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务商品名称',
  `ex_sale_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部服务提供方订单号',
  `member_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户姓名',
  `member_mobile` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户手机号',
  `member_card_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户身份证号',
  `member_address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户服务地址',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区',
  `service_time` datetime(0) NULL DEFAULT NULL COMMENT '预约服务时间',
  `order_status` tinyint(2) NULL DEFAULT NULL COMMENT '订单状态',
  `finish_time` datetime(0) NULL DEFAULT NULL COMMENT '订单完成时间',
  `file_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文件 json 格式',
  `remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '订单备注',
  `callback_state` tinyint(2) NULL DEFAULT NULL COMMENT '回调状态',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  `proxy_item_id` bigint(20) NULL DEFAULT NULL COMMENT '商户商品ID',
  `proxy_product_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品编码',
  `phone_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '下单手机号',
  `change_balance` decimal(10, 2) NULL DEFAULT NULL COMMENT '变化预算',
  `callback_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调url',
  `short_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短链',
  `used_callback_time` datetime(0) NULL DEFAULT NULL COMMENT '使用回调商户时间',
  `used_callback_state` int(4) NULL DEFAULT NULL COMMENT '使用回调状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`business_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '星灿订单记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_refund_nan_hang_integral_order
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_refund_nan_hang_integral_order`;
CREATE TABLE `tb_rc_refund_nan_hang_integral_order`  (
  `id` bigint(19) NOT NULL COMMENT 'ID',
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商订单号',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商id',
  `item_face_price` int(10) NOT NULL DEFAULT 0 COMMENT '面值,积分退款数量',
  `user_name` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户姓名',
  `user_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户id，南航会员卡号',
  `phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '电话号码',
  `order_state` tinyint(2) NOT NULL DEFAULT 0 COMMENT '退款状态（0 退回成功\r\n1 退回失败\r\n2 退回中）',
  `result_msg` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款失败原因',
  `recharge_integral_order_id` bigint(19) NOT NULL DEFAULT 0 COMMENT '原充值订单id',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款原因',
  `supplier_refund_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款供应商退款订单id',
  `push_time_begin` datetime(0) NULL DEFAULT NULL COMMENT '推送开始时间',
  `push_time_end` datetime(0) NULL DEFAULT NULL COMMENT '推送结束时间',
  `sync_result_time` datetime(0) NULL DEFAULT NULL COMMENT '同步结果时间',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_supplier_refund_biz_id`(`supplier_refund_biz_id`) USING BTREE,
  INDEX `idx_order_id`(`recharge_integral_order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '南航里程积分退款记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_refund_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_refund_record`;
CREATE TABLE `tb_rc_refund_record`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `proxy_id` bigint(19) NOT NULL COMMENT '商户id',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户编码',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `product_type` tinyint(2) NOT NULL COMMENT '产品类型',
  `order_state` tinyint(2) NOT NULL COMMENT '退款状态（0 退回成功1 退回失败2 退回中）',
  `service_id` bigint(19) NOT NULL COMMENT '售后服务单id，即退款请求订单号',
  `code_num` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '需要退款的兑换码',
  `business_order_id` bigint(19) NOT NULL COMMENT '业务订单号，下单订单号',
  `order_id` bigint(19) NOT NULL COMMENT '订单主键id',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款原因',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE,
  INDEX `idx_service_id`(`service_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通用退款记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_repush_order_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_repush_order_log`;
CREATE TABLE `tb_rc_repush_order_log`  (
  `id` bigint(19) NOT NULL,
  `proxy_id` bigint(19) NOT NULL COMMENT '代理商ID',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代理商编码',
  `proxy_custom_biz_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方业务流水',
  `push_order_content` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补发订单内容',
  `push_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '补发人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_proxy_order_id`(`proxy_id`, `proxy_custom_biz_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '荣数补发订单日志' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_request_log
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_request_log`;
CREATE TABLE `tb_rc_request_log`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `order_id` bigint(19) NULL DEFAULT NULL COMMENT '订单id',
  `log_type` tinyint(2) NULL DEFAULT NULL COMMENT '日志类型：0、推送日志，1、查询日志，2、回调日志',
  `response_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '响应内容',
  `push_time_begin` datetime(0) NULL DEFAULT NULL,
  `push_time_end` datetime(0) NULL DEFAULT NULL,
  `push_time_total` bigint(10) NULL DEFAULT NULL,
  `exception_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '异常信息内容',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_rs_refund_check_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_rs_refund_check_record`;
CREATE TABLE `tb_rc_rs_refund_check_record`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `proxy_id` bigint(19) NOT NULL COMMENT '商户id',
  `proxy_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户编码',
  `sp_type` tinyint(2) NOT NULL COMMENT '运营商类型',
  `product_type` tinyint(2) NOT NULL COMMENT '产品类型',
  `sip_order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务订单号，下单订单号',
  `vendor_order_no` bigint(19) NOT NULL COMMENT '订单主键id',
  `voucher_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单类型为卡券时，不为空',
  `fund_status` tinyint(2) NOT NULL COMMENT '退款状态',
  `message` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退货失败原因',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除',
  `voucher_code` bigint(19) NULL DEFAULT NULL COMMENT '充值中心券ID或是充值中心卡密ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '荣数退货记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_shandong_alliance_proxy
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_shandong_alliance_proxy`;
CREATE TABLE `tb_rc_shandong_alliance_proxy`  (
  `id` bigint(19) NOT NULL,
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `proxy_id` bigint(19) NOT NULL COMMENT '商户ID',
  `coupon_sign_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发券接口sign_key',
  `direct_recharge3des_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '直充接口3deskey',
  `direct_recharge_sign_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '直充接口签名key',
  `app_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'appId',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '山东联盟信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_short_url_map
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_short_url_map`;
CREATE TABLE `tb_rc_short_url_map`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `lurl` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '长地址',
  `surl` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '短链地址',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tb_rc_short_url_map_pk`(`surl`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_sms_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_sms_record`;
CREATE TABLE `tb_rc_sms_record`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `receive_message_phone_no` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接收短信手机号码',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '短信内容',
  `send_message_state` tinyint(2) NOT NULL DEFAULT 0 COMMENT '发送短信状态，0初始化,1发送中，2发送成功，3发送失败',
  `sms_source` tinyint(2) NOT NULL COMMENT '短信来源',
  `source_id` bigint(19) NULL DEFAULT NULL COMMENT '来源标识',
  `source_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源的其他信息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_sp_type_config
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_sp_type_config`;
CREATE TABLE `tb_rc_sp_type_config`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `code` int(11) NOT NULL COMMENT '运营商编码',
  `text` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '运营商名称',
  `product_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '充值产品类型，逗号分隔',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `sync_recharge` tinyint(2) NOT NULL DEFAULT 0 COMMENT '同步充值',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运营商类型配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_sp_type_config_copy1
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_sp_type_config_copy1`;
CREATE TABLE `tb_rc_sp_type_config_copy1`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `code` int(11) NOT NULL COMMENT '运营商编码',
  `text` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '运营商名称',
  `product_types` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '充值产品类型，逗号分隔',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  `sync_recharge` tinyint(2) NOT NULL DEFAULT 0 COMMENT '同步充值',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_code`(`code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '运营商类型配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_spc_product
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_spc_product`;
CREATE TABLE `tb_rc_spc_product`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `pro_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品编号',
  `pro_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `product_type` tinyint(2) NULL DEFAULT NULL COMMENT '产品类型',
  `charge_type` tinyint(2) NULL DEFAULT NULL COMMENT '商品类型',
  `sp_type` tinyint(2) NULL DEFAULT NULL COMMENT '运营商类型',
  `picture` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品配图',
  `use_notice` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用说明',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_pro_no`(`pro_no`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'SPC商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_spc_product_detail
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_spc_product_detail`;
CREATE TABLE `tb_rc_spc_product_detail`  (
  `id` bigint(19) NOT NULL COMMENT '主键id',
  `spc_product_id` bigint(19) NOT NULL COMMENT 'spc商品主键',
  `mv_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '面额编号',
  `face_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '面值',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'SPC商品面值明细' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_wechat_add_budget_record
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_wechat_add_budget_record`;
CREATE TABLE `tb_rc_wechat_add_budget_record`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '逻辑删除，0-可用，1-删除',
  `proxy_id` bigint(19) NULL DEFAULT NULL COMMENT '商户ID',
  `proxy_item_id` bigint(19) NULL DEFAULT NULL COMMENT '商户商品ID',
  `product_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品ID',
  `proxy_item_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户商品信息',
  `denomination` decimal(12, 2) NULL DEFAULT NULL COMMENT '面值',
  `transaction_minimum` decimal(12, 2) NULL DEFAULT NULL COMMENT '门槛',
  `coupon_use_rule` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '面额信息',
  `mch_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户号',
  `supplier_id` bigint(19) NULL DEFAULT NULL COMMENT '供应商ID',
  `batch_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次号',
  `batch_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次名称',
  `stock_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次类型',
  `before_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '追加前金额',
  `before_budget_max_amount_by_day` bigint(19) NULL DEFAULT NULL COMMENT '单天发放上限X个',
  `add_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '追加金额',
  `add_num` int(11) NULL DEFAULT NULL COMMENT '追加个数',
  `target_add_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '目标最终预算',
  `target_budget_max_amount_by_day` bigint(19) NULL DEFAULT NULL COMMENT '目标最终单天发放上限个数',
  `after_budget` decimal(12, 2) NULL DEFAULT NULL COMMENT '追加后金额',
  `after_budget_max_amount_by_day` bigint(19) NULL DEFAULT NULL COMMENT '单天发放上限Y个',
  `state` tinyint(2) NULL DEFAULT NULL COMMENT '状态',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `submit_time` datetime(0) NULL DEFAULT NULL COMMENT '提交时间',
  `finish_time` datetime(0) NULL DEFAULT NULL COMMENT '任务执行完成时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信批次预算自动追加记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_wechat_add_budget_record_batch_snapshot
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_wechat_add_budget_record_batch_snapshot`;
CREATE TABLE `tb_rc_wechat_add_budget_record_batch_snapshot`  (
  `id` bigint(19) NOT NULL COMMENT 'id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '逻辑删除，0-可用，1-删除',
  `add_budget_record_id` bigint(19) NULL DEFAULT NULL COMMENT '微信批次预算自动追加记录任务ID',
  `snapshot_type` tinyint(2) NULL DEFAULT NULL COMMENT '快照类型,0-提交任务前,1-提交任务后',
  `batch_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次',
  `distributed_coupons` bigint(19) NULL DEFAULT NULL COMMENT '已发券数量',
  `coupon_amount` decimal(12, 2) NULL DEFAULT NULL COMMENT '面值',
  `distributed_amount` decimal(12, 2) NULL DEFAULT NULL COMMENT '已发预算',
  `batch_snapshot` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '批次快照',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信批次预算自动追加记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_wechat_app
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_wechat_app`;
CREATE TABLE `tb_rc_wechat_app`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `app_id` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用id',
  `app_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用昵称',
  `app_type` int(200) NULL DEFAULT NULL COMMENT '应用类型',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_app_id`(`app_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信公众号信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_wechat_receive_callback
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_wechat_receive_callback`;
CREATE TABLE `tb_rc_wechat_receive_callback`  (
  `id` bigint(19) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL DEFAULT 0 COMMENT '删除状态',
  `stock_creator_mchid` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建批次的商户号',
  `stock_id` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '批次号',
  `coupon_id` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '代金券id',
  `single_price_max` bigint(19) NULL DEFAULT NULL COMMENT '单品最高优惠价格',
  `cut_to_price` bigint(19) NULL DEFAULT NULL COMMENT '减至后优惠单价',
  `max_price` bigint(19) NULL DEFAULT NULL COMMENT '最高价格',
  `coupon_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代金券名称',
  `status` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '代金券状态',
  `receive_time` datetime(0) NULL DEFAULT NULL COMMENT '领券时间',
  `coupon_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '券类型',
  `no_cash` tinyint(1) NULL DEFAULT NULL COMMENT '是否无资金流',
  `available_begin_time` datetime(0) NULL DEFAULT NULL COMMENT '可用开始时间',
  `available_end_time` datetime(0) NULL DEFAULT NULL COMMENT '可用结束时间',
  `singleitem` tinyint(4) NULL DEFAULT NULL COMMENT '是否单品优惠',
  `coupon_amount` bigint(19) NULL DEFAULT NULL COMMENT '面额',
  `transaction_minimum` bigint(19) NULL DEFAULT NULL COMMENT '门槛',
  `consume_time` datetime(0) NULL DEFAULT NULL COMMENT '核销时间',
  `consume_mchid` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销商户号',
  `transaction_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销订单号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `coupon_idx`(`coupon_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信接回调信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for tb_rc_wx_user
-- ----------------------------
DROP TABLE IF EXISTS `tb_rc_wx_user`;
CREATE TABLE `tb_rc_wx_user`  (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted` tinyint(2) NULL DEFAULT NULL COMMENT '逻辑删除',
  `mp_upc_user_id` bigint(20) NULL DEFAULT NULL COMMENT '公众号账号对应的用户平台中心的用户id',
  `mp_open_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公众号账号对应的open_id',
  `mp_appid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公众号账号对应的appid',
  `mini_upc_user_id` bigint(20) NULL DEFAULT NULL COMMENT '小程序账号对应的用户平台中心的用户id',
  `mini_open_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序账号对应的open_id',
  `mini_appid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序账号对应的appid',
  `union_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开放平台unionid',
  `real_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `nick_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `mobile_area_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号号段',
  `mobile` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `gender` tinyint(2) NULL DEFAULT NULL COMMENT '性别：0-未知，1-男，2-女',
  `register_time` datetime(0) NULL DEFAULT NULL COMMENT '注册时间',
  `register_type` tinyint(2) NULL DEFAULT NULL COMMENT '注册方式：1-小程序授权注册；2-公众号授权注册; 3-短信验证码注册',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_mini_upc_user_id`(`mini_upc_user_id`) USING BTREE,
  INDEX `idx_mobile`(`mobile`) USING BTREE,
  INDEX `idx_mp_upc_user_id`(`mp_upc_user_id`) USING BTREE,
  INDEX `idx_real_name`(`real_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信用户' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
