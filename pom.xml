<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<packaging>pom</packaging>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.18</version> <!-- lookup parent from repository -->
	</parent>
	<groupId>cn.joysim.cloud</groupId>
	<artifactId>j-cloud-saas-recharge-center</artifactId>
	<version>*******</version>
	<name>j-cloud-saas-recharge-center</name>
	<description>JoySim Cloud Development Foundation Framework project for Spring Boot</description>

	<properties>
		<java.version>1.8</java.version>
		<j-cloud-dependencies.version>1.2.1</j-cloud-dependencies.version>
		<j-cloud-common.version>*******</j-cloud-common.version>
		<j-cloud-config-consumer.version>1.2.1</j-cloud-config-consumer.version>
		<j-cloud-config-provider.version>1.2.1</j-cloud-config-provider.version>
		<j-cloud-config-redis.version>*******</j-cloud-config-redis.version>
		<j-cloud-config-mybatis.version>*******</j-cloud-config-mybatis.version>
		<j-cloud-config-job.version>1.2.1</j-cloud-config-job.version>
		<j-cloud-paas-psc.version>2.3.0</j-cloud-paas-psc.version>
		<j-cloud-paas-upc.version>2.4.1</j-cloud-paas-upc.version>
		<j-cloud-paas-msc.version>2.3.0</j-cloud-paas-msc.version>
		<j-cloud-paas-fsc.version>2.3.0</j-cloud-paas-fsc.version>
		<j-cloud-paas-wcc.version>2.6.1</j-cloud-paas-wcc.version>
		<j-cloud-paas-fsc.version>2.3.0</j-cloud-paas-fsc.version>
		<easyexcel.version>3.3.2</easyexcel.version>
		<poi-ooxml.version>5.2.3</poi-ooxml.version>
		<lock4j-redis.version>2.2.5</lock4j-redis.version>
		<binarywang.version>4.5.0</binarywang.version>
		<redisson.version>3.22.0</redisson.version>
		<commons-email.version>1.5</commons-email.version>
		<alipay.sdk.version>4.40.251.ALL</alipay.sdk.version>
		<joysim.meituan.marketing>1.0.9</joysim.meituan.marketing>
		<joysim.yj.version>1.0.3-SNAPSHOT</joysim.yj.version>
		<reflections.version>0.10.2</reflections.version>
		<aliyun.gateway.version>1.1.7</aliyun.gateway.version>
		<aliyun-java-sdk-core.version>4.5.13</aliyun-java-sdk-core.version>
		<aliyun-java-sdk-afs.version>1.0.1</aliyun-java-sdk-afs.version>
		<spring-cloud-starter-alicloud-oss.version>2.2.0.RELEASE</spring-cloud-starter-alicloud-oss.version>
		<tika.version>2.9.0</tika.version>
		<snakeyaml.version>2.3</snakeyaml.version>
		<jettison.version>1.5.4</jettison.version>
		<spring-kafka.version>2.9.13</spring-kafka.version>
		<kafka-clients.version>3.7.2</kafka-clients.version>
		<spring-web.version>5.3.39</spring-web.version>
		<guava.version>33.4.0-jre</guava.version>
		<commons-compress.version>1.27.1</commons-compress.version>
		<tomcat.version>9.0.90</tomcat.version>
		<json-path.version>2.9.0</json-path.version>
		<commons-io.version>2.18.0</commons-io.version>
		<netty-common.version>4.1.116.Final</netty-common.version>
		<psbc.sop.public.tools.version>0.0.1</psbc.sop.public.tools.version>
		<pfpj.sm.version>2.0.3</pfpj.sm.version>
		<skipTests>true</skipTests>
		<profiles.dir>src/main/profiles</profiles.dir>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

	</properties>

	<dependencyManagement>
		<!-- j-cloud dependency -->
		<dependencies>
			<dependency>
				<groupId>io.netty</groupId>
				<artifactId>netty-common</artifactId>
				<version>${netty-common.version}</version>
			</dependency>
			<dependency>
				<groupId>commons-io</groupId>
				<artifactId>commons-io</artifactId>
				<version>${commons-io.version}</version>
			</dependency>
			<dependency>
				<groupId>com.jayway.jsonpath</groupId>
				<artifactId>json-path</artifactId>
				<version>${json-path.version}</version>
			</dependency>
			<!-- tomcat jar start -->
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-core</artifactId>
				<version>${tomcat.version}</version>
			</dependency>

			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-el</artifactId>
				<version>${tomcat.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tomcat.embed</groupId>
				<artifactId>tomcat-embed-websocket</artifactId>
				<version>${tomcat.version}</version>
			</dependency>
			<!-- tomcat jar end -->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-compress</artifactId>
				<version>${commons-compress.version}</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>${guava.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-web</artifactId>
				<version>${spring-web.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-webmvc</artifactId>
				<version>${spring-web.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.kafka</groupId>
				<artifactId>kafka-clients</artifactId>
				<version>${kafka-clients.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.kafka</groupId>
				<artifactId>spring-kafka</artifactId>
				<version>${spring-kafka.version}</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.yaml/snakeyaml -->
			<dependency>
				<groupId>org.yaml</groupId>
				<artifactId>snakeyaml</artifactId>
				<version>${snakeyaml.version}</version>
			</dependency>

			<dependency>
				<groupId>org.codehaus.jettison</groupId>
				<artifactId>jettison</artifactId>
				<version>${jettison.version}</version>
			</dependency>

			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-dependencies</artifactId>
				<version>${j-cloud-dependencies.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<!-- j-cloud -->
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-common</artifactId>
				<version>${j-cloud-common.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-config-consumer</artifactId>
				<version>${j-cloud-config-consumer.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-config-provider</artifactId>
				<version>${j-cloud-config-provider.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-config-redis</artifactId>
				<version>${j-cloud-config-redis.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-config-mybatis</artifactId>
				<version>${j-cloud-config-mybatis.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-config-job</artifactId>
				<version>${j-cloud-config-job.version}</version>
			</dependency>

			<!-- psc dependencies -->
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-psc-common</artifactId>
				<version>${j-cloud-paas-psc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-psc-api-open</artifactId>
				<version>${j-cloud-paas-psc.version}</version>
			</dependency>

			<!-- upc dependencies -->
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-upc-common</artifactId>
				<version>${j-cloud-paas-upc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-upc-auth</artifactId>
				<version>${j-cloud-paas-upc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-upc-api-open</artifactId>
				<version>${j-cloud-paas-upc.version}</version>
			</dependency>

			<!-- msc dependencies -->
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-msc-common</artifactId>
				<version>${j-cloud-paas-msc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-msc-api-open</artifactId>
				<version>${j-cloud-paas-msc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-fsc-common</artifactId>
				<version>${j-cloud-paas-fsc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-fsc-api-open</artifactId>
				<version>${j-cloud-paas-fsc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-fsc-auth</artifactId>
				<version>${j-cloud-paas-fsc.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>1.2.83</version>
			</dependency>
			<dependency>
				<groupId>com.google.code.gson</groupId>
				<artifactId>gson</artifactId>
				<version>2.8.9</version>
			</dependency>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk15to18</artifactId>
				<version>1.69</version>
			</dependency>
			<dependency>
				<groupId>org.bouncycastle</groupId>
				<artifactId>bcprov-jdk15on</artifactId>
				<version>1.68</version>
			</dependency>

			<!-- wcc 微信中控 -->
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-wcc-common</artifactId>
				<version>${j-cloud-paas-wcc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-wcc-api-open</artifactId>
				<version>${j-cloud-paas-wcc.version}</version>
			</dependency>
			<dependency>
				<groupId>cn.joysim.cloud</groupId>
				<artifactId>j-cloud-paas-wcc-auth</artifactId>
				<version>${j-cloud-paas-wcc.version}</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/com.alibaba/easyexcel -->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>easyexcel</artifactId>
				<version>${easyexcel.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.poi</groupId>
				<artifactId>poi-ooxml</artifactId>
				<version>${poi-ooxml.version}</version>
			</dependency>

			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
				<version>${lock4j-redis.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.binarywang</groupId>
				<artifactId>weixin-java-common</artifactId>
				<version>${binarywang.version}</version>
			</dependency>
			<dependency>
				<groupId>com.github.binarywang</groupId>
				<artifactId>weixin-java-miniapp</artifactId>
				<version>${binarywang.version}</version>
			</dependency>

			<dependency>
				<groupId>com.github.binarywang</groupId>
				<artifactId>weixin-java-pay</artifactId>
				<version>${binarywang.version}</version>
			</dependency>

			<dependency>
				<groupId>com.github.binarywang</groupId>
				<artifactId>weixin-java-mp</artifactId>
				<version>${binarywang.version}</version>
			</dependency>

			<dependency>
				<groupId>cn.joysim.third</groupId>
				<artifactId>joysim-meituan-marketing</artifactId>
				<version>${joysim.meituan.marketing}</version>
			</dependency>

			<!--alipay-sdk-->
			<dependency>
				<groupId>com.alipay.sdk</groupId>
				<artifactId>alipay-sdk-java</artifactId>
				<version>${alipay.sdk.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun.api.gateway</groupId>
				<artifactId>sdk-core-java</artifactId>
				<version>${aliyun.gateway.version}</version>
			</dependency>

			<dependency>
				<groupId>cn.joysim.third</groupId>
				<artifactId>joysim-yj-coupon-common</artifactId>
				<version>${joysim.yj.version}</version>
			</dependency>

			<dependency>
				<groupId>cn.joysim.third</groupId>
				<artifactId>joysim-yj-coupon-core</artifactId>
				<version>${joysim.yj.version}</version>
			</dependency>

			<dependency>
				<groupId>org.reflections</groupId>
				<artifactId>reflections</artifactId>
				<version>${reflections.version}</version>
			</dependency>

			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-core</artifactId>
				<version>${aliyun-java-sdk-core.version}</version>
			</dependency>
			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-afs</artifactId>
				<version>${aliyun-java-sdk-afs.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-starter-alicloud-oss</artifactId>
				<version>${spring-cloud-starter-alicloud-oss.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.tika</groupId>
				<artifactId>tika-core</artifactId>
				<version>${tika.version}</version>
			</dependency>
			<dependency>
				<groupId>com.psbc.sop.public.tools</groupId>
				<artifactId>sm</artifactId>
				<version>${psbc.sop.public.tools.version}</version>
			</dependency>
			<dependency>
				<groupId>com.pfpj.sm</groupId>
				<artifactId>sm-util</artifactId>
				<version>${pfpj.sm.version}</version>
			</dependency>
			<dependency>
				<groupId>com.mysql</groupId>
				<artifactId>mysql-connector-j</artifactId>
				<version>8.3.0</version>
			</dependency>

		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.2.0</version>
			</plugin>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>2.8.2</version>
				<configuration>
					<skip>false</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.openrewrite.maven</groupId>
				<artifactId>rewrite-maven-plugin</artifactId>
				<version>5.46.1</version>
				<configuration>
					<exportDatatables>true</exportDatatables>
					<activeRecipes>
						<recipe>org.openrewrite.java.dependencies.DependencyVulnerabilityCheck</recipe>
					</activeRecipes>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.openrewrite.recipe</groupId>
						<artifactId>rewrite-java-dependencies</artifactId>
						<version>1.24.1</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>

	<!-- 私仓发布配置 -->
	<distributionManagement>
		<repository>
			<id>joysim-releases</id>
			<name>joysim-releases</name>
			<url>http://nexus.joysim.cn/repository/joysim-release/</url>
		</repository>
		<snapshotRepository>
			<id>joysim-snapshots</id>
			<name>joysim-snapshots</name>
			<url>http://nexus.joysim.cn/repository/joysim-snapshot/</url>
		</snapshotRepository>
	</distributionManagement>

	<developers>
		<developer>
			<email><EMAIL></email>
		</developer>
	</developers>


	<modules>  <module>app-api-inner</module>
		<module>app-common/j-cloud-saas-recharge-center-api-open</module>
		<module>app-common/j-cloud-saas-recharge-center-common</module>
		<module>app-config</module>
		<module>app-boot</module>
		<module>app-consumer</module>
		<module>app-provider</module>
		<module>app-test</module>
	</modules>
</project>
