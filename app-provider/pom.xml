<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>j-cloud-saas-recharge-center</artifactId>
        <groupId>cn.joysim.cloud</groupId>
        <version>*******</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>app-provider</artifactId>

    <description>服务提供模块，业务逻辑实现、DAO、定时任务</description>
    <modules>
        <module>provider-rc</module>
        <module>job-rc</module>
        <module>proxy-rc</module>
    </modules>

    <packaging>pom</packaging>

    <dependencies>
        <dependency>
            <groupId>cn.joysim.cloud</groupId>
            <artifactId>j-cloud-config-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.joysim.cloud</groupId>
            <artifactId>j-cloud-config-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.joysim.cloud</groupId>
            <artifactId>j-cloud-config-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>lock4j-redis-template-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-mp</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.joysim.third</groupId>
            <artifactId>joysim-meituan-marketing</artifactId>

        </dependency>

        <!--alipay-sdk-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
        </dependency>


        <dependency>
            <groupId>cn.joysim.third</groupId>
            <artifactId>joysim-yj-coupon-common</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.joysim.third</groupId>
            <artifactId>joysim-yj-coupon-core</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.joysim.third</groupId>
            <artifactId>joysim-qy-api-core</artifactId>
            <version>1.0.9-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <developers>
        <developer>
            <email><EMAIL></email>
        </developer>
    </developers>
</project>
