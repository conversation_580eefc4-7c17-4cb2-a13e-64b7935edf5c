package cn.joysim.cloud.rc.job.config;

import cn.hutool.core.util.IdUtil;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.concurrent.ThreadLocalRandom;

@Slf4j
public class MdcXxlJobInterceptor extends IJobHandler {
    private final IJobHandler delegate;

    public MdcXxlJobInterceptor(IJobHandler delegate) {
        this.delegate = delegate;
    }

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        // 前置处理
        MDC.put(RCAppConst.MDC_REQUEST_ID, IdUtil.simpleUUID());

        try {
            // 实际执行
            return delegate.execute(param);
        } finally {
            MDC.remove(RCAppConst.MDC_REQUEST_ID);
        }
    }
}