package cn.joysim.cloud.rc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.model.dto.PageQueryDTO;
import cn.joysim.cloud.common.model.dto.PageResultDTO;
import cn.joysim.cloud.common.model.pojo.enums.DataLogicState;
import cn.joysim.cloud.common.service.impl.CRUDServiceImpl;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.common.util.PageUtil;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.common.exception.ChannelSupplierException;
import cn.joysim.cloud.rc.common.exception.RechargeException;
import cn.joysim.cloud.rc.common.exception.status.ChannelSupplierStatusCode;
import cn.joysim.cloud.rc.common.exception.status.RechargeStatusCode;
import cn.joysim.cloud.rc.common.model.pojo.enums.*;
import cn.joysim.cloud.rc.common.utils.BigDecimalUtil;
import cn.joysim.cloud.rc.common.utils.JsonContext;
import cn.joysim.cloud.rc.mapper.ChannelSupplierItemInfoMapper;
import cn.joysim.cloud.rc.model.bo.*;
import cn.joysim.cloud.rc.model.dto.ChannelSupplierInfoDTO;
import cn.joysim.cloud.rc.model.dto.ChannelSupplierItemChangeLogDTO;
import cn.joysim.cloud.rc.model.dto.ChannelSupplierItemInfoDTO;
import cn.joysim.cloud.rc.model.dto.WxFavorStocksDTO;
import cn.joysim.cloud.rc.model.dto.supplierItem.ChannelSupplierItemBatchChangeImportDTO;
import cn.joysim.cloud.rc.model.dto.supplierItem.ChannelSupplierItemBatchImportDTO;
import cn.joysim.cloud.rc.model.dto.supplierItem.ChannelSupplierItemInfoAdminPageDTO;
import cn.joysim.cloud.rc.model.dto.supplierItem.ChannelSupplierItemInfoOptionsPageDTO;
import cn.joysim.cloud.rc.model.pojo.ChannelSupplierItemInfo;
import cn.joysim.cloud.rc.model.query.ChannelSupplierItemInfoOptionsQuery;
import cn.joysim.cloud.rc.model.query.ChannelSupplierItemInfoQuery;
import cn.joysim.cloud.rc.service.*;
import cn.joysim.cloud.rc.service.activity.ChannelCommonActivityService;
import cn.joysim.cloud.rc.service.cardCipher.CardCipherCodeService;
import cn.joysim.cloud.rc.service.oilCard.OilEntityCardService;
import cn.joysim.cloud.rc.service.supplier.ChannelSupplierItemChangeLogService;
import cn.joysim.cloud.rc.service.virtualCoupon.SupplierAlipayRequestService;
import cn.joysim.cloud.rc.utils.BeanArrayUtil;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.DiscountInfoConfig;
import com.alipay.api.response.AlipayUserDtbankcustChannelvoucherconfigQueryResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.ValidationException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@Validated
public class ChannelSupplierItemInfoServiceImpl
        extends CRUDServiceImpl<ChannelSupplierItemInfoDTO, ChannelSupplierItemInfo, ChannelSupplierItemInfoMapper>
        implements ChannelSupplierItemInfoService {

    private static final String CACHE_KEK_FORMAT = "supplier:item:%d:%d:%d";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    @Lazy
    private OilEntityCardService oilEntityCardService;

    @Resource
    @Lazy
    private CardCipherCodeService cardCipherCodeService;


    @Resource
    private ChannelSupplierItemChangeLogService channelSupplierItemChangeLogService;

    @Resource
    @Lazy
    private ChannelSupplierInfoService channelSupplierInfoService;

    @Resource
    private SpTypeService spTypeService;

    @Resource
    private RCWxPayService rcWxPayService;

    @Resource
    @Lazy
    private ChannelCommonActivityService channelCommonActivityService;

    @Resource
    private AliPayOpenApiService aliPayOpenApiService;

    @Resource
    private SupplierAlipayRequestService supplierAlipayRequestService;


    @SneakyThrows
    @Override
    @Transactional
    public boolean saveEntity(String agentName, ChannelSupplierItemInfoDTO dto) {

        //校验除了AAA以外，dto的唯一
//        if (!RCAppConst.RECHARGE_SUPPLIER_ITEM_ID_UNKNOWN.equals(dto.getSupplierItemId())) {
//            Optional<ChannelSupplierItemInfo> channelSupplierItemInfoOpt = this.lambdaQuery().eq(ChannelSupplierItemInfo::getSupplierItemId, dto.getSupplierItemId()).oneOpt();
//            if (channelSupplierItemInfoOpt.isPresent()) {
//                throw new ChannelSupplierException(ChannelSupplierStatusCode.SUPPLIER_PRODUCT_ID_UK);
//            }
//        }
        log.info("agentName= {} 请求对象={}",JSONUtil.toJsonStr(dto));
        supplierItemIdUkIdCheck(dto, null);
        //供应商展示名称，若未填写，创建时自动填入供应商商品名称。
        if (StrUtil.isBlank(dto.getSupplierItemDisplayName())){
            dto.setSupplierItemDisplayName(dto.getSupplierItemName());
        }
        ChannelSupplierItemInfo entity = BeanUtil.toBean(dto, ChannelSupplierItemInfo.class);
        log.info("ChannelSupplierItemInfo={}",JSONUtil.toJsonStr(entity));
        entity.init();
        if (null != dto.getExpireRadio() && ChannelSupplierItemInfoDTO.EXPIRE_TIME.equals(dto.getExpireRadio())){
            entity.setExchangeExpireDay(null);
        }
        if (null != dto.getExpireRadio() && ChannelSupplierItemInfoDTO.EXPIRE_DAY.equals(dto.getExpireRadio())){
            entity.setExchangeExpireTime(null);
        }

        log.info("ChannelSupplierItemInfo2={}",JSONUtil.toJsonStr(entity));

        boolean save = this.save(entity);
        dto.setId(entity.getId());

        ChannelSupplierItemChangeLogDTO changeLog = new ChannelSupplierItemChangeLogDTO();
        changeLog.setOperatingAccount(agentName);
        changeLog.setPreValue("");
        changeLog.setNewValue(entity.getSupplierItemId());
        changeLog.setChannelSupplierItemId(entity.getId());
        ChannelSupplierItemInfoDTO itemInfoDTO = this.getById(entity.getId());


        changeLog.setNewAllValue(JsonContext.mapper.writeValueAsString(itemInfoDTO));

        log.info("changeLog={}",JSONUtil.toJsonStr(changeLog));

        channelSupplierItemChangeLogService.saveEntity(changeLog);

        channelCommonActivityService.processActivityForSupplierItem(dto,agentName);

        ChannelSupplierItemInfoDTO dbData = this.getById(entity.getId());
        checkFaceValue(itemInfoDTO.getSupplierId(),itemInfoDTO.getSupplierItemId(), dbData);

        return save;
    }

    @SneakyThrows
    @Override
    @Transactional
    public boolean updateEntityById(String agentName, ChannelSupplierItemInfoDTO itemInfoDTO) {

        //校验除了AAA以外，dto的唯一
        supplierItemIdUkIdCheck(itemInfoDTO, itemInfoDTO.getId());

        ChannelSupplierItemInfoDTO dbData = this.getById(itemInfoDTO.getId());
        //供应商展示名称，若未填写，更新时自动填入供应商商品名称。
        if (StrUtil.isBlank(itemInfoDTO.getSupplierItemDisplayName())){
            itemInfoDTO.setSupplierItemDisplayName(itemInfoDTO.getSupplierItemName());
        }


        if (Arrays.asList(SpType.WX.getCode(),SpType.MEI_TUAN_VOUCHER.getCode(),SpType.ALIPAY_RED_PACKET.getCode()).contains(dbData.getSpType())){
            if (dbData.getItemFacePrice().compareTo(itemInfoDTO.getItemFacePrice())!=0) {
                throw new RechargeException(RechargeStatusCode.ITEM_FACE_PRICE_CHANGE);
            }
        }
        checkFaceValue(itemInfoDTO.getSupplierId(),itemInfoDTO.getSupplierItemId(), dbData);

        ChannelSupplierItemInfo entity = BeanUtil.toBean(itemInfoDTO, ChannelSupplierItemInfo.class);
        entity.setUpdateTime(new Date());

        if (null != itemInfoDTO.getExpireRadio() && ChannelSupplierItemInfoDTO.EXPIRE_TIME.equals(itemInfoDTO.getExpireRadio())){
            entity.setExchangeExpireDay(null);
        }
        if (null != itemInfoDTO.getExpireRadio() && ChannelSupplierItemInfoDTO.EXPIRE_DAY.equals(itemInfoDTO.getExpireRadio())){
            entity.setExchangeExpireTime(null);
        }


        log.info("供应商商品修改，entity={};itemInfoDTO={}",JSONUtil.toJsonStr(entity),JSONUtil.toJsonStr(itemInfoDTO));


        ChannelSupplierItemChangeLogDTO changeLog = new ChannelSupplierItemChangeLogDTO();
        changeLog.setOperatingAccount(agentName);
        changeLog.setPreValue(dbData.getSupplierItemId());
        //旧供应商编码
        String supplierItemIdOld = dbData.getSupplierItemId();
        //旧供应商面值
        BigDecimal itemFacePriceOld = dbData.getItemFacePrice();
        //旧供应商结算价格
        BigDecimal itemSellPriceOld = dbData.getItemSellPrice();
        changeLog.setNewValue(itemInfoDTO.getSupplierItemId());
        changeLog.setChannelSupplierItemId(entity.getId());


        changeLog.setPreAllValue(JsonContext.mapper.writeValueAsString(dbData));



        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, null, itemInfoDTO.getId(), SupplierProductType.ITEM.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, null, itemInfoDTO.getId(), SupplierProductType.ITEM_PACKET.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, itemInfoDTO.getSupplierId(), itemInfoDTO.getId(), SupplierProductType.ITEM.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, itemInfoDTO.getSupplierId(), itemInfoDTO.getId(), SupplierProductType.ITEM_PACKET.getCode()));
        boolean result = this.updateById(entity);
        if (result){
            channelCommonActivityService.processActivityForSupplierItem(dbData,itemInfoDTO,agentName);

            ChannelSupplierItemInfoDTO newItemInfo = this.getById(entity.getId());
            String supplierItemIdNew = newItemInfo.getSupplierItemId();
            BigDecimal itemSellPriceNew = newItemInfo.getItemSellPrice();
            BigDecimal itemFacePriceNew = newItemInfo.getItemFacePrice();
            changeLog.setNewAllValue(JsonContext.mapper.writeValueAsString(newItemInfo));
            //商品条码，面值，结算价格都没变化，不保存修改记录
            boolean supplierItemIdFLag = (StrUtil.isNotBlank(supplierItemIdOld) && StrUtil.isBlank(supplierItemIdNew)) || (StrUtil.isBlank(supplierItemIdOld) && StrUtil.isNotBlank(supplierItemIdNew))
                    || (StrUtil.isNotBlank(supplierItemIdOld) && StrUtil.isNotBlank(supplierItemIdNew) && !supplierItemIdNew.equals(supplierItemIdOld));
            boolean itemFacePriceFlag = (null == itemFacePriceOld && null != itemFacePriceNew) || (null != itemFacePriceOld && null == itemFacePriceNew) || (null != itemFacePriceOld && itemFacePriceNew.compareTo(itemFacePriceOld) != 0);
            boolean itemSellPriceFlag = (null == itemSellPriceNew && null != itemSellPriceOld) || (null != itemSellPriceNew && null == itemSellPriceOld) || (null != itemSellPriceNew && itemSellPriceNew.compareTo(itemSellPriceOld) != 0);
            boolean enabledFlag = (null == dbData.getEnabled() && null != itemInfoDTO.getEnabled()) || (null != dbData.getEnabled() && null == itemInfoDTO.getEnabled()) || (null != dbData.getEnabled() && null != itemInfoDTO.getEnabled() && dbData.getEnabled() != itemInfoDTO.getEnabled());
            if (supplierItemIdFLag || itemFacePriceFlag || itemSellPriceFlag||enabledFlag){
                channelSupplierItemChangeLogService.saveEntity(changeLog);
            }
        }

        return result;
    }

    /**
     * 供应商商品除了AAA外的唯一性校验
     *
     * @param dto
     * @param excludeId
     */
    @Override
    public void supplierItemIdUkIdCheck(ChannelSupplierItemInfoDTO dto, Long excludeId) {
        if (RCAppConst.RECHARGE_SUPPLIER_ITEM_ID_UNKNOWN.equals(dto.getSupplierItemId())) {
            return;
        }

        //ESM供应商产品跳过唯一校验
        ChannelSupplierInfoDTO supplierInfoDTO = channelSupplierInfoService.getById(dto.getSupplierId());
        SpTypeConfigBO spType = spTypeService.getSpType(dto.getSpType());
        if (supplierInfoDTO.getSupplierType() == SupplierType.ESM && "ESM".equals(spType.getText())) {
            return;
        }

        Optional<ChannelSupplierItemInfo> channelSupplierItemInfoOpt = this.lambdaQuery().eq(ChannelSupplierItemInfo::getSupplierItemId, dto.getSupplierItemId()).eq(ChannelSupplierItemInfo::getDeleted,DataLogicState.NOT_DELETED).oneOpt();

        if (Objects.nonNull(excludeId)) {
            if (channelSupplierItemInfoOpt.isPresent() && !channelSupplierItemInfoOpt.get().getId().equals(excludeId)) {
                throw new ChannelSupplierException(ChannelSupplierStatusCode.SUPPLIER_PRODUCT_ID_UK);
            }
        } else {
            if (channelSupplierItemInfoOpt.isPresent()) {
                throw new ChannelSupplierException(ChannelSupplierStatusCode.SUPPLIER_PRODUCT_ID_UK);
            }
        }
    }


    @Override
    public PageResultDTO<ChannelSupplierItemInfoAdminPageDTO> page(PageQueryDTO<ChannelSupplierItemInfoQuery> pageQuery) {
        ChannelSupplierItemInfoQuery params = pageQuery.getParams();
        Page<ChannelSupplierItemInfoAdminPageDTO> page = PageUtil.getPage(pageQuery);
        List<ChannelSupplierItemInfoAdminPageDTO> list = this.baseMapper.selectAdminPage(page, params);
        page.setRecords(list);

        PageResultDTO<ChannelSupplierItemInfoAdminPageDTO> pageResult = PageUtil.getPageResult(page, ChannelSupplierItemInfoAdminPageDTO.class);
        return pageResult;
    }

    @Override
    public List<ChannelSupplierItemInfoAdminPageDTO> export(ChannelSupplierItemInfoQuery params) {

        PageQueryDTO pageQuery = new PageQueryDTO();
        pageQuery.setCurrent(0);
        pageQuery.setSize(-1);
        pageQuery.setParams(params);

        Page<ChannelSupplierItemInfoAdminPageDTO> page = PageUtil.getPage(pageQuery);
        List<ChannelSupplierItemInfoAdminPageDTO> list = this.baseMapper.selectAdminPage(page, params);
        return list;
    }

    @Override
    public ChannelSupplierItemOptimalBO selectOptimalSupplierItem(Long supplierId, Long productId, BigDecimal denomination, Integer spType, RechargeProductType productType, String rechargeLabel) {
        log.info("获取最优(充值顺序)充值供应商产品，供应商充值顺序 = 优先级 / 心跳权重, supplierId = {}, productId = {}, denomination = {}, spType = {}",
                supplierId, productId, denomination, spType);

        List<ChannelSupplierItemOptimalBO> list;
        // 检查是否需要检查供应商不支持的充值标签
        if (StrUtil.isBlank(rechargeLabel)) {
            list = this.baseMapper.selectMatchSupplierItem(supplierId, productId, denomination, spType, productType);
        } else {
            list = this.baseMapper.selectMatchSupplierItemWithLabel(supplierId, productId, denomination, spType, productType, rechargeLabel);
        }
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        log.info("支持的充值产品 = {}", JSONUtil.toJsonStr(list));
        // 过滤特殊产品（油卡短信充值、卡密充值）
        List<ChannelSupplierItemOptimalBO> filterList = handleFilterList(list);
        log.info("过滤后支持的充值产品 = {}", JSONUtil.toJsonStr(filterList));
        if (CollUtil.isEmpty(filterList)) {
            return null;
        }
        if (filterList.size() == 1) {
            log.info("支持的产品结果唯一，无需排序");
            return list.get(0);
        }
        for (ChannelSupplierItemOptimalBO channelSupplierItemOptimalBO : filterList) {
            Integer heartbeatWeight = 1;
            Double sortIndex = 1.0d;
            Object heartbeatWeightObj = redisTemplate.opsForValue().get(RCAppConst.SUPPLIER_HEARTBEAT_WEIGHT_REDIS_PREFIX + channelSupplierItemOptimalBO.getSupplierId());
            if (heartbeatWeightObj != null) {
                heartbeatWeight = (Integer) heartbeatWeightObj;
                if (heartbeatWeight == 0) {
                    heartbeatWeight = 1;
                }
            }
            if (channelSupplierItemOptimalBO.getSupplierPriority() != null) {
                sortIndex = Double.valueOf(channelSupplierItemOptimalBO.getSupplierPriority());
            }
            channelSupplierItemOptimalBO.setSortIndex(sortIndex / heartbeatWeight);
        }
        filterList.sort(Comparator.comparing(ChannelSupplierItemOptimalBO::getSortIndex).reversed());
        log.info("支持的产品排序后结果 = {}", JSONUtil.toJsonStr(filterList));
        return filterList.get(0);
    }

    @Override
    public List<ChannelSupplierItemCouponBO> selectCouponSupplierItem(Long supplierId, Long productId, SupplierProductType supplierProductType) {
        log.info("卡券下单，供应商单券商品，供应商:{},产品Id:{},产品类型:{}",supplierId,productId,supplierProductType );
        List<ChannelSupplierItemCouponBO> list = new ArrayList<>();
        String key = String.format(CACHE_KEK_FORMAT, supplierId, productId, supplierProductType.getCode());
        Object obj = redisTemplate.opsForValue().get(key);
        if (obj == null) {
            if (SupplierProductType.ITEM.equals(supplierProductType)) {
                list = this.baseMapper.selectCouponSupplierItem(supplierId, productId);
                list.stream().forEach(item -> item.setSupplierProductType(SupplierProductType.ITEM));
                log.info("卡券下单，供应商单券商品，供应商:{},产品Id:{},对应的产品:{}", supplierId, productId, list);
                if (CollUtil.isNotEmpty(list)) {
                    redisTemplate.opsForValue().set(key, list, 5, TimeUnit.MINUTES);
                }
            } else if (SupplierProductType.ITEM_PACKET.equals(supplierProductType)) {
                list = this.baseMapper.selectCouponPacketSupplierItem(supplierId, productId);
                list.stream().forEach(item -> item.setSupplierProductType(SupplierProductType.ITEM_PACKET));
                log.info("卡券下单，供应商组合礼包商品，供应商:{},产品Id:{},对应的产品:{}", supplierId, productId, list);
                //券包商品不加缓存，因为单个商品变换的时候，券包不一定能实时感知到
            }
        } else {
            list = (List<ChannelSupplierItemCouponBO>) obj;
            log.info("从redis缓存获取数据:{}",list);
        }
        return list;
    }


    @Override
    public List<ChannelSupplierGeneralPacketItemBO> selectGenerateRechargeSupplierItem(Long supplierId, Long productId, SupplierProductType supplierProductType) {
        log.info("通用充值下单，供应商单券商品，供应商:{},产品Id:{},产品类型:{}",supplierId,productId,supplierProductType );
        List<ChannelSupplierGeneralPacketItemBO> list = new ArrayList<>();

        if (SupplierProductType.ITEM.equals(supplierProductType)) {
            list = this.baseMapper.selectGeneralSupplierItem(supplierId, productId);
            list.stream().forEach(item -> item.setSupplierProductType(SupplierProductType.ITEM));
            log.info("通用充值下单，供应商单券商品，供应商:{},产品Id:{},对应的产品:{}", supplierId, productId, list);
        } else if (SupplierProductType.RECHARGE_PACKET.equals(supplierProductType)) {
            list = this.baseMapper.selectGeneralPacketSupplierItem(supplierId, productId);
            list.stream().forEach(item -> item.setSupplierProductType(SupplierProductType.RECHARGE_PACKET));
            log.info("通用充值下单，供应商组合礼包商品，供应商:{},产品Id:{},对应的产品:{}", supplierId, productId, list);
        }

        return list;
    }

    @Override
    public ChannelSupplierItemOptimalBO selectSupplierItemById(Long id, RechargeProductType productType) {
        return this.baseMapper.selectSupplierItemById(id,productType);
    }

    @Override
    public ChannelSupplierItemInfoDTO selectBySupplierItem(Long id) {
        ChannelSupplierItemInfo channelSupplierItemInfo = this.baseMapper.selectById(id);
        if (null != channelSupplierItemInfo){
            return BeanUtil.toBean(channelSupplierItemInfo,ChannelSupplierItemInfoDTO.class);
        }
        return null;
    }

    @Override
    public List<ChannelSupplierItemInfoDTO> selectBySupplierIdAndSupplierItemId(Long supplierId, String supplierItemId) {
        LambdaQueryWrapper<ChannelSupplierItemInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ChannelSupplierItemInfo::getSupplierId,supplierId);
        wrapper.eq(ChannelSupplierItemInfo::getSupplierItemId,supplierItemId);
        List<ChannelSupplierItemInfo> list = list(wrapper);
        if (CollUtil.isNotEmpty(list)){
            return BeanArrayUtil.copyListProperties(list,ChannelSupplierItemInfoDTO::new);
        }
        return null;
    }

    /**
     * 选择列表分页查询
     *
     * @param
     * @return
     */
    @Override
    public PageResultDTO<ChannelSupplierItemInfoOptionsPageDTO> optionsPage(PageQueryDTO<ChannelSupplierItemInfoOptionsQuery> pageQuery) {
        ChannelSupplierItemInfoOptionsQuery query = pageQuery.getParams();
        Page<ChannelSupplierItemInfoOptionsPageDTO> page = PageUtil.getPage(pageQuery);
        List<ChannelSupplierItemInfoOptionsPageDTO> list = this.baseMapper.optionsPage(page, query);
        page.setRecords(list);
        return PageUtil.getPageResult(page, ChannelSupplierItemInfoOptionsPageDTO.class);
    }

    @SneakyThrows
    @Override
    @Transactional
    public void updateSupplierItemIdByExcel(String agentName, @Valid ChannelSupplierItemBatchChangeImportDTO datum) {
        Long supplierItemId = Long.valueOf(datum.getId());
        ChannelSupplierItemInfoDTO infoDTO = this.getById(supplierItemId);
        if (Objects.isNull(infoDTO)) {
            throw new ValidationException("供应商商品ID不存在");
        }
        if (StrUtil.isNotBlank(datum.getSupplierCode())) {
            ChannelSupplierInfoDTO channelSupplierInfoDTO = channelSupplierInfoService.getBySupplierCodeWithCache(datum.getSupplierCode());
            if (Objects.isNull(channelSupplierInfoDTO)) {
                throw new ValidationException("供应商不存在");
            }
        }

        ChannelSupplierItemChangeLogDTO changeLog = new ChannelSupplierItemChangeLogDTO();
        changeLog.setPreAllValue(JsonContext.mapper.writeValueAsString(infoDTO));


        Long checkSupplierId = null;

        ChannelSupplierItemInfoDTO hisSupplierItemInfoDTO = BeanUtil.toBean(infoDTO, ChannelSupplierItemInfoDTO.class);
        if (StrUtil.isNotBlank(datum.getSupplierCode())) {
            ChannelSupplierInfoDTO channelSupplierInfoDTO = channelSupplierInfoService.getBySupplierCodeWithCache(datum.getSupplierCode());
            checkSupplierId= channelSupplierInfoDTO.getId();
        }else{
            checkSupplierId = hisSupplierItemInfoDTO.getId();
        }

        checkFaceValue(checkSupplierId,datum.getSupplierItemId(), hisSupplierItemInfoDTO);


        infoDTO.setSupplierItemId(datum.getSupplierItemId());
        LambdaUpdateChainWrapper<ChannelSupplierItemInfo> updateWrapper = this.lambdaUpdate();
        if (StrUtil.isNotBlank(datum.getSupplierItemId())){
            updateWrapper.set(ChannelSupplierItemInfo::getSupplierItemId, datum.getSupplierItemId());
        }
        if (StrUtil.isNotBlank(datum.getItemSellPrice())){
            updateWrapper.set(ChannelSupplierItemInfo::getItemSellPrice,new BigDecimal(datum.getItemSellPrice()));
        }
        if (StrUtil.isNotBlank(datum.getSupplierCode())) {
            ChannelSupplierInfoDTO channelSupplierInfoDTO = channelSupplierInfoService.getBySupplierCodeWithCache(datum.getSupplierCode());
            updateWrapper.set(ChannelSupplierItemInfo::getSupplierId, channelSupplierInfoDTO.getId());
        }
        updateWrapper.set(ChannelSupplierItemInfo::getUpdateTime,new Date());
        boolean update = updateWrapper.eq(ChannelSupplierItemInfo::getId, datum.getId()).update();
        if (!update) {
            throw new ValidationException("未知原因更新失败");
        }

        changeLog.setOperatingAccount(agentName);
        if (StrUtil.isNotBlank(datum.getSupplierItemId())){
            changeLog.setPreValue(hisSupplierItemInfoDTO.getSupplierItemId());
            changeLog.setNewValue(datum.getSupplierItemId());
        }
        if (StrUtil.isNotBlank(datum.getItemSellPrice())){
            changeLog.setPreItemSellPriceValue(infoDTO.getItemSellPrice());
            changeLog.setNewItemSellPriceValue(new BigDecimal(datum.getItemSellPrice()));
        }


        ChannelSupplierItemInfoDTO newItemInfo = this.getById(supplierItemId);
        changeLog.setNewAllValue(JsonContext.mapper.writeValueAsString(newItemInfo));

        changeLog.setChannelSupplierItemId(supplierItemId);


        channelSupplierItemChangeLogService.saveEntity(changeLog);
        channelCommonActivityService.processActivityForSupplierItem(hisSupplierItemInfoDTO,newItemInfo,agentName);
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, null, infoDTO.getId(), SupplierProductType.ITEM.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, null, infoDTO.getId(), SupplierProductType.ITEM_PACKET.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, infoDTO.getSupplierId(), infoDTO.getId(), SupplierProductType.ITEM.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, infoDTO.getSupplierId(), infoDTO.getId(), SupplierProductType.ITEM_PACKET.getCode()));

    }

    private void checkFaceValue(Long supplierId,String supplierItemId, ChannelSupplierItemInfoDTO hisSupplierItemInfoDTO) {
        //如果是微信，判断新输入的条码面值和原面值是否一致，不一致，则提示更新失败
        if (SpType.WX.getCode().equals(hisSupplierItemInfoDTO.getSpType()) && StrUtil.isNotBlank(supplierItemId)){
            //分
            int hisItemFacePrice = hisSupplierItemInfoDTO.getItemFacePrice().multiply(BigDecimal.valueOf(100)).intValue();
            String supplierItemIdParam = supplierItemId;
            WxFavorStocksDTO favorStocksDTO = rcWxPayService.getFavorStocksV3(supplierItemIdParam,supplierId);
            if (null != favorStocksDTO && null != favorStocksDTO.getStocksGetResult() && null != favorStocksDTO.getStocksGetResult().getStockUseRule()
                    && null != favorStocksDTO.getStocksGetResult().getStockUseRule().getFixedNormalCoupon()
                    && null != favorStocksDTO.getStocksGetResult().getStockUseRule().getFixedNormalCoupon().getCouponAmount()){
                Integer couponAmount = favorStocksDTO.getStocksGetResult().getStockUseRule().getFixedNormalCoupon().getCouponAmount();
                if (couponAmount != hisItemFacePrice){
                    log.error("面值不一致，旧供应商商品条码={}，新供应商条码={};couponAmount={};hisItemFacePrice={}",
                            supplierItemIdParam, supplierItemId,couponAmount,hisItemFacePrice);
                    throw new ValidationException("更新失败，供应商品条码信息面值有误");
                }
            }else {
                log.error("获取详情信息失败，旧供应商商品条码={}，新供应商条码={}",
                        supplierItemIdParam,supplierItemId);
                throw new ValidationException("更新失败，供应商品条码信息有误");
            }
        }
        if (SpType.ALIPAY_RED_PACKET.getCode().equals(hisSupplierItemInfoDTO.getSpType()) && StrUtil.isNotBlank(supplierItemId)) {
            ChannelSupplierInfoDTO channelSupplierInfoDTO = channelSupplierInfoService.getById(supplierId);
            SupplierAliPayConfigBO aliPayConfigBO = JSON.parse(channelSupplierInfoDTO.getSupplierConfig(), SupplierAliPayConfigBO.class);
            AlipayClient alipayClient = aliPayOpenApiService.getAlipayClient(aliPayConfigBO.getAppId());

            AlipayUserDtbankcustChannelvoucherconfigQueryResponse channelVoucherConfig = supplierAlipayRequestService.getChannelVoucherConfig(alipayClient, supplierItemId);
            DiscountInfoConfig discountInfoConfig = channelVoucherConfig.getDiscountInfoConfig();
            if (discountInfoConfig.getDiscountType().equals("reduce")) {
                if (BigDecimalUtil.fen2yuan(Long.valueOf(discountInfoConfig.getDiscountValue())).compareTo(hisSupplierItemInfoDTO.getItemFacePrice()) != 0) {
                    throw new ValidationException("更新失败，供应商品条码信息面值有误");
                }
            }
        }

    }

    @Override
    public List<ChannelSupplierItemInfoDTO> getByIds(List<Long> supplierProducts) {
        List<ChannelSupplierItemInfo> list = this.lambdaQuery().in(ChannelSupplierItemInfo::getId, supplierProducts).eq(ChannelSupplierItemInfo::getDeleted, DataLogicState.NOT_DELETED).list();
        List<ChannelSupplierItemInfoDTO> channelSupplierItemInfoDTOS = BeanArrayUtil.copyListProperties(list, ChannelSupplierItemInfoDTO::new);
        return channelSupplierItemInfoDTOS;
    }

    @Override
    @Transactional
    public String batchImportProduct(String agentName, List<ChannelSupplierItemBatchImportDTO> dataList,Long supplierId) {
        StringBuilder str = new StringBuilder();
        for (int i = 0; i < dataList.size(); i++){
            ChannelSupplierItemBatchImportDTO data = dataList.get(i);
            String productTypeStr = data.getProductTypeStr();
            if (StrUtil.isNotBlank(productTypeStr)) {
                RechargeProductType rechargeProductType = RechargeProductType.fromText(productTypeStr);
                if (null == rechargeProductType) {
                    str.append("产品类型错误，请检查，产品类型名称：").append(productTypeStr);
                    return str.toString();
                }
                data.setProductType(RechargeProductType.fromText(productTypeStr));
            }
            String spTypeStr = data.getSpTypeStr();
            if (StrUtil.isNotBlank(spTypeStr)) {
                SpTypeConfigBO spTypeConfigBO = spTypeService.getSpType(spTypeStr);
                if (null == spTypeConfigBO) {
                    str.append("供应商类型错误，请检查，供应商名称：").append(spTypeStr);
                    return str.toString();
                }
                data.setSpType(spTypeConfigBO.getCode());
            }
            String couponTypeStr = data.getCouponTypeStr();
            if (StrUtil.isNotBlank(couponTypeStr)) {
                CouponType couponType = CouponType.fromText(couponTypeStr);
                data.setCouponType(couponType);
            }
            //判断参数是否为空
            int rowIndex = i + 2;
            if (null == data.getProductType()){
                str.append("第").append(rowIndex).append("行【产品类型】不能为空");
                return str.toString();
            }

            if (null == data.getSpType()){
                str.append("第").append(rowIndex).append("行【运营商类型】不能为空");
                return str.toString();
            }

            if (StrUtil.isBlank(data.getSupplierItemId())){
                str.append("第").append(rowIndex).append("行【供应商商品条码】不能为空");
                return str.toString();
            }

            if (StrUtil.isBlank(data.getSupplierItemName())){
                str.append("第").append(rowIndex).append("行【供应商商品名称】不能为空");
                return str.toString();
            }

            if (null == data.getItemFacePrice()){
                str.append("第").append(rowIndex).append("行【面值】不能为空");
                return str.toString();
            }

            if (null == data.getItemSellPrice()){
                str.append("第").append(rowIndex).append("行【结算价格】不能为空");
                return str.toString();
            }
            ChannelSupplierItemInfoDTO itemInfoDTO = BeanUtil.toBean(data, ChannelSupplierItemInfoDTO.class);
            if (null == data.getEnabled()) {
                itemInfoDTO.setEnabled(true);
            }
            itemInfoDTO.setSupplierId(supplierId);
            if (StrUtil.isBlank(itemInfoDTO.getSupplierItemDisplayName())){
                itemInfoDTO.setSupplierItemDisplayName(itemInfoDTO.getSupplierItemName());
            }
            saveEntity(agentName, itemInfoDTO);
            channelCommonActivityService.processActivityForSupplierItem(itemInfoDTO,agentName);

        }
        log.info("供应商商品导入返回信息={}", str.toString());
        return str.toString();
    }

    /**
     * 根据供应商编码获取供应商商品
     *
     * @param supplierItemId
     * @return
     */
    @Override
    public Optional<ChannelSupplierItemInfoDTO> getBySupplierItemId(String supplierItemId) {
        Optional<ChannelSupplierItemInfo> channelSupplierItemInfo = this.lambdaQuery().eq(ChannelSupplierItemInfo::getSupplierItemId, supplierItemId).oneOpt();
        return channelSupplierItemInfo.map(item -> BeanUtil.toBean(item, ChannelSupplierItemInfoDTO.class));
    }

    private List<ChannelSupplierItemOptimalBO> handleFilterList(List<ChannelSupplierItemOptimalBO> sourceList) {
        return sourceList.stream().filter(x -> {
            // 中石化短信卡充需检查卡密库存是否充足
            if (SupplierType.OF_ZSH_DXCZ == x.getSupplierType() || SupplierType.JXZY_ZSH_DXCZ == x.getSupplierType()) {
                int availableEntityCardCount = oilEntityCardService.selectAvailableEntityCardCount(x.getItemFacePrice().intValue());
                if (availableEntityCardCount > 0) {
                    return true;
                }
                log.warn("中石化 短信充值卡密不足，面额= {}，供应商 = {}", x.getItemFacePrice(), x.getSupplierType().getText());
                return false;
            } else if (SupplierType.JXZY_KM == x.getSupplierType()) {
                int availableEntityCardCount = cardCipherCodeService.selectAvailableCardCipherCodeCount(x.getSpType(), x.getItemFacePrice().intValue());
                if (availableEntityCardCount > 0) {
                    return true;
                }
                log.warn("{} 卡密充值卡密不足，面额= {}，供应商 = {}", x.getSpType(), x.getItemFacePrice(), x.getSupplierType().getText());
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    @Override
    public  void  initChannelSupplierItemInfoBatch(){
        List<ChannelSupplierItemInfo> list = this.lambdaQuery().in(ChannelSupplierItemInfo::getSpType, SpType.WX, SpType.ALIPAY_RED_PACKET, SpType.MEI_TUAN_VOUCHER).list();
        if (CollUtil.isEmpty(list)){
            return;
        }
        for (ChannelSupplierItemInfo item : list) {
            ChannelSupplierItemInfoDTO supplierItemInfoDTO = BeanUtil.toBean(item, ChannelSupplierItemInfoDTO.class);
            try {
                channelCommonActivityService.processActivityForSupplierItem(supplierItemInfoDTO, "system");
            } catch (Exception e) {
                log.info("初始化商品的活动异常：{}",e.getMessage());
                continue;
            }
        }
    }

    @SneakyThrows
    @Override
    public boolean updateEnabledStatus(String agentName, Long id, Boolean enabled) {

        ChannelSupplierItemInfoDTO hisItemInfoDTO = this.getById(id);
        boolean update = this.lambdaUpdate().set(ChannelSupplierItemInfo::getEnabled, enabled).eq(ChannelSupplierItemInfo::getId, id).update();

        ChannelSupplierItemInfoDTO infoDTO = this.getById(id);

        ChannelSupplierItemChangeLogDTO changeLog = new ChannelSupplierItemChangeLogDTO();
        changeLog.setOperatingAccount(agentName);
        changeLog.setPreValue(hisItemInfoDTO.getSupplierItemId());
        changeLog.setNewValue(infoDTO.getSupplierItemId());
        changeLog.setPreItemSellPriceValue(hisItemInfoDTO.getItemSellPrice());
        changeLog.setNewItemSellPriceValue(infoDTO.getItemSellPrice());
        changeLog.setChannelSupplierItemId(id);
        changeLog.setPreAllValue(JsonContext.mapper.writeValueAsString(hisItemInfoDTO));
        changeLog.setNewAllValue(JsonContext.mapper.writeValueAsString(infoDTO));
        channelSupplierItemChangeLogService.saveEntity(changeLog);

        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, null, infoDTO.getId(), SupplierProductType.ITEM.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, null, infoDTO.getId(), SupplierProductType.ITEM_PACKET.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, infoDTO.getSupplierId(), infoDTO.getId(), SupplierProductType.ITEM.getCode()));
        redisTemplate.delete(String.format(CACHE_KEK_FORMAT, infoDTO.getSupplierId(), infoDTO.getId(), SupplierProductType.ITEM_PACKET.getCode()));
        return update;
    }

    @Override
    public boolean removeDownloadDayBillByBySupplierItemId(String batchId) {
        Optional<ChannelSupplierItemInfoDTO> channelSupplierItemInfoOpt = this.getBySupplierItemId(batchId);
        if (channelSupplierItemInfoOpt.isPresent()) {
            boolean update = this.lambdaUpdate().set(ChannelSupplierItemInfo::getDownloadDayBill, false).eq(ChannelSupplierItemInfo::getId, channelSupplierItemInfoOpt.get().getId()).update();
            return update;
        }
        return false;
    }
}
