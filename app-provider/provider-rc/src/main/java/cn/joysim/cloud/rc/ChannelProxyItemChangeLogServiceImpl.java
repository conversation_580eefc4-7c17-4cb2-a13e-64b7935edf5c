package cn.joysim.cloud.rc;

import cn.hutool.core.util.StrUtil;
import cn.joysim.cloud.common.model.dto.PageQueryDTO;
import cn.joysim.cloud.common.model.dto.PageResultDTO;
import cn.joysim.cloud.common.service.impl.CRUDServiceImpl;
import cn.joysim.cloud.common.util.PageUtil;
import cn.joysim.cloud.rc.common.utils.JsonContext;
import cn.joysim.cloud.rc.mapper.ChannelProxyItemChangeLogMapper;
import cn.joysim.cloud.rc.model.dto.ChannelProxyItemChangeLogAdminPageDTO;
import cn.joysim.cloud.rc.model.dto.ChannelProxyItemChangeLogDTO;
import cn.joysim.cloud.rc.model.dto.ChannelProxyItemInfoDTO;
import cn.joysim.cloud.rc.model.pojo.ChannelProxyItemChangeLog;
import cn.joysim.cloud.rc.model.query.ChannelProxyItemChangeLogAdminPageQuery;
import cn.joysim.cloud.rc.service.ChannelProxyItemChangeLogService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class ChannelProxyItemChangeLogServiceImpl extends CRUDServiceImpl<ChannelProxyItemChangeLogDTO, ChannelProxyItemChangeLog,ChannelProxyItemChangeLogMapper> implements ChannelProxyItemChangeLogService {


    @Override
    public PageResultDTO<ChannelProxyItemChangeLogAdminPageDTO> selectAdminPage(PageQueryDTO<ChannelProxyItemChangeLogAdminPageQuery> pageQuery) {
        ChannelProxyItemChangeLogAdminPageQuery params = pageQuery.getParams();
        Page<ChannelProxyItemChangeLogAdminPageDTO> page = PageUtil.getPage(pageQuery);
        List<ChannelProxyItemChangeLogAdminPageDTO> list = this.baseMapper.selectAdminPage(page, params);
        page.setRecords(list);
        return PageUtil.getPageResult(page, ChannelProxyItemChangeLogAdminPageDTO.class);
    }

    @SneakyThrows
    @Override
    public void saveMethod(String agentName, Long proxyItemId, ChannelProxyItemInfoDTO preValue, ChannelProxyItemInfoDTO newValue) {
        ChannelProxyItemChangeLog channelProxyItemChangeLog = new ChannelProxyItemChangeLog();
        channelProxyItemChangeLog.init();
        channelProxyItemChangeLog.setChannelProxyItemId(proxyItemId);
        channelProxyItemChangeLog.setOperatingAccount(agentName);
        BigDecimal itemFacePriceOld = null;
        BigDecimal itemSellPriceOld = null;
        String productIdOld = "";
        Long supplierProductIdOld = null;
        Boolean enabledOld=null;
        if (Objects.nonNull(preValue)) {
            channelProxyItemChangeLog.setPreValue(JsonContext.mapper.writeValueAsString(preValue));
            itemFacePriceOld = preValue.getItemFacePrice();
            itemSellPriceOld = preValue.getItemSellPrice();
            productIdOld = preValue.getProductId();
            supplierProductIdOld = preValue.getSupplierProductId();
            enabledOld=preValue.getEnabled();
        }
        BigDecimal itemFacePriceNew = null;
        BigDecimal itemSellPriceNew = null;
        String productIdNew = "";
        Long supplierProductIdNew = null;
        Boolean enabledNew=null;
        if (Objects.nonNull(newValue)) {
            channelProxyItemChangeLog.setNewValue(JsonContext.mapper.writeValueAsString(newValue));
            itemFacePriceNew = newValue.getItemFacePrice();
            itemSellPriceNew = newValue.getItemSellPrice();
            productIdNew = newValue.getProductId();
            supplierProductIdNew = newValue.getSupplierProductId();
            enabledNew=newValue.getEnabled();
        }

        boolean itemFacePriceFlag = (null == itemFacePriceOld && null != itemFacePriceNew) || (null != itemFacePriceOld && null == itemFacePriceNew) || (null != itemFacePriceOld && itemFacePriceOld.compareTo(itemFacePriceNew) != 0);
        boolean itemSellPriceFlag = (itemSellPriceOld == null && null != itemSellPriceNew) || (itemSellPriceOld != null && null == itemSellPriceNew) || (null != itemSellPriceOld && itemSellPriceOld.compareTo(itemSellPriceNew) != 0);
        boolean productIdFlag = (StrUtil.isBlank(productIdOld)  && StrUtil.isNotBlank(productIdNew)) || (StrUtil.isNotBlank(productIdOld)  && StrUtil.isBlank(productIdNew)) || (StrUtil.isNotBlank(productIdOld) && StrUtil.isNotBlank(productIdNew) && !productIdOld.equals(productIdNew));
        boolean supplierProductIdFlag = (null == supplierProductIdOld && null != supplierProductIdNew) || (null != supplierProductIdOld && null == supplierProductIdNew) || (null != supplierProductIdOld && !supplierProductIdOld.equals(supplierProductIdNew));
        boolean enableFlag=(null ==enabledOld && null != enabledNew) || (null != enabledOld && null == enabledNew) ||(null !=enabledOld &&!enabledOld.equals(enabledNew));
        if (itemFacePriceFlag || itemSellPriceFlag || productIdFlag || supplierProductIdFlag||enableFlag) {
            this.save(channelProxyItemChangeLog);
        }

    }
}