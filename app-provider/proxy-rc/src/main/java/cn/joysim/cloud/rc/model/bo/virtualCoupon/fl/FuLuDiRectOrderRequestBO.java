package cn.joysim.cloud.rc.model.bo.virtualCoupon.fl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:luo<PERSON>nhui
 * @create: 2024-03-12 15:01
 * @Description: 福禄直充下单请求参数
 */
@Data
public class FuLuDiRectOrderRequestBO implements Serializable {

    /**
     * 商品编号
     */
    @JsonProperty("product_id")
    private Long productId;

    /**
     * 外部订单号
     */
    @JsonProperty("customer_order_no")
    private String customerOrderNo;

    /**
     * 充值账号
     */
    @JsonProperty("charge_account")
    private String chargeAccount;

    /**
     * 购买数量
     */
    @JsonProperty("buy_num")
    private Integer buyNum;
}