package cn.joysim.cloud.rc.model.bo.mobilePhone.ytfl;

import lombok.Data;

import java.io.Serializable;

/**
 * @author:l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-07-31 17:50
 * @Description: 易通峰联查询接口返回对象
 */
@Data
public class YtFlOrderQueryResultBO implements Serializable {

    /**
     * 0表示排队中
     */
    public static final Integer BE_QUEUE = 0;

    /**
     * 1表示充值中
     */
    public static final Integer  RECHARGING = 1;


    /**
     * 2表示充值成功，最终态
     */
    public static final Integer RECHARGE_SUCCESS = 2;

    /**
     * 3表示失败
     */
    public static final Integer RECHARGE_FAILED = 3;

    /**
     * 5表示退款状态、
     */
    public static final Integer REFUNDING = 5;

    /**
     * 6退款成功，失败状态码，最终态
     */
    public static final Integer REFUND_SUCCESS = 6;

    /**
     * 7部分退款(正常不会出现，作为预留)
     */
    public static final Integer PART_REFUND = 7;



    /**
     * 响应码
     */
    private String resultCode;

    /**
     * 代理编号
     */
    private String agentCode;

    /**
     * 请求订单编号，与下单保持一致
     */
    private String orderId;

    /**
     * 手机号码
     */
    private String phoneNo;

    /**
     * 扣款金额
     */
    private String amount;

    /**
     * 充值状态
     * 状态，0表示排队中，1表示充值中、2表示充值成功、3表示失败、5表示退款状态、6退款成功，7部分退款(正常不会出现，作为预留)，
     * 备注：2成功，6失败这两个是最终状态，0，1，3，5，7 为中间状态按照处理中处理
     */
    private Integer rechargeState;

    /**
     * 签名结果
     */
    private String hmac;
}