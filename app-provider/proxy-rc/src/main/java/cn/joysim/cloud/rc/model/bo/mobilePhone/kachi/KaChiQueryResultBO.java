package cn.joysim.cloud.rc.model.bo.mobilePhone.kachi;

import lombok.Data;

import java.io.Serializable;

/**
 * @author:l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2023-02-08 17:29
 * @Description: 卡池订单状态查询结果对象
 */
@Data
public class KaChiQueryResultBO extends KachiBaseResultBO implements Serializable {

    /**
     * 成功成功
     */
    public static final String QUERY_RECHARGE_SUCCESS = "2";

    /**
     * 充值失败
     */
    public static  final String QUERY_RECHARGE_FAILED = "3";

    /**
     * 话费充值流水凭证
     */
    private String moredata;

    public KaChiQueryResultBO() {

    }
}