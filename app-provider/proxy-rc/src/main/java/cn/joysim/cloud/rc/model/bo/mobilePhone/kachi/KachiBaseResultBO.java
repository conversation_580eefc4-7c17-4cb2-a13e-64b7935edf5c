package cn.joysim.cloud.rc.model.bo.mobilePhone.kachi;

import lombok.Data;

import java.io.Serializable;

/**
 * @author:luo<PERSON><PERSON><PERSON>
 * @create: 2023-02-09 10:13
 * @Description: 卡池基础返回对象
 */
@Data
public class KachiBaseResultBO implements Serializable {

    public static final String PARAM_ERROR_CODE = "-1";

    /**
     * 订单已入库
     */
    public static String SUCCESS_CODE = "T00001";

    /**
     * 状态查询，查询成功状态码
     */
    public static String QUERY_SUCCESS_CODE = "T00002";

    /**
     * 账户余额不足
     */
    public static String BALANCE_NOT_ENOUGH = "T00006";


    /**
     * 下单失败，查询失败
     */
    public static String FAIL_CODE = "T00007";

    /**
     * 系统异常 system exception
     * 类似网络超时，返回值不能正常解析等情况,需核实处理(不可直接将订单失败，可通过查询订单接口处理)
     */
    public static String SYSTEM_EXCEPTION = "T00008";

    /**
     * T00009 订单号已存在
     * 平台已存在该订单号，订单具体状态应查询或核实处理
     */
    public static String ORDER_EXIST = "T00009";

    /**
     * 充值成功code，recharge_success_code 回调状态码
     */
    public static String RECHARGE_SUCCESS_CODE = "T00003";

    /**
     * 充值失败code,recharge_fail_code 回调状态码
     */
    public static String RECHARGE_FAIL_CODE = "T00004";

    /**
     * 订单不存在
     * 下单请求成功并且平台返回流水号，30分钟后所查订单不存在，可失败处理
     */
    public static String ORDER_NOT_EXIST = "T00010";


    private String resultCode;

    /**
     * 下单成功时，resultMsg为平台订单号
     */
    private String resultMsg;

}