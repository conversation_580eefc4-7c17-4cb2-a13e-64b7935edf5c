package cn.joysim.cloud.rc.model.bo.virtualCoupon.fl;

import cn.joysim.cloud.rc.model.bo.virtualCoupon.SupplierFuLuConfigBO;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-03-12 15:45
 * @Description: 福禄下单公用请求参数
 */
@Data
public class FuLuRequestBaseBO implements Serializable {


    /**
     * 默认版本号
     */
    public static final String DEFAULT_VERSION = "2.0";
    /**
     * 接口请求或响应格式
     */
    public static final String DEFAULT_FORMAT = "json";

    /**
     * 请求使用的编码格式
     */
    public static final String DEFAULT_CHARSET = "utf-8";

    /**
     * 签名加密类型
     */
    public static final String DEFAULT_SIGN_TYPE = "md5";


    /**
     * 分配给商户的app_key
     */
    @JsonProperty("app_key")
    private String appKey;

    /**
     * 接口方法名称
     */
    private String method;

    /**
     * 时间戳，格式为：yyyy-MM-dd HH:mm:ss
     */
    private String timestamp;

    /**
     * 调用的接口版本 2.0
     */
    private String version;

    /**
     * 接口请求或响应格式
     */
    private String format;

    /**
     * 请求使用的编码格式,utf-8
     */
    private String charset;

    /**
     * 签名加密类型，目前仅支持md5
     */
    @JsonProperty("sign_type")
    private String signType;


    /**
     * 	签名串
     */
    private String sign;

    /**
     * 授权码，固定值为“”
     */
    @JsonProperty("app_auth_token")
    private String appAuthToken;

    /**
     * 请求参数
     */
    @JsonProperty("biz_content")
    private String bizContent;

    public static FuLuRequestBaseBO init(SupplierFuLuConfigBO configBO,String timeStamp,String method){
        FuLuRequestBaseBO requestBaseBO = new FuLuRequestBaseBO();
        requestBaseBO.setAppKey(configBO.getAppKey());
        requestBaseBO.setMethod(method);
        requestBaseBO.setTimestamp(timeStamp);
        requestBaseBO.setVersion(DEFAULT_VERSION);
        requestBaseBO.setFormat(DEFAULT_FORMAT);
        requestBaseBO.setCharset(DEFAULT_CHARSET);
        requestBaseBO.setSignType(DEFAULT_SIGN_TYPE);
        requestBaseBO.setAppAuthToken("");
        return requestBaseBO;
    }
}