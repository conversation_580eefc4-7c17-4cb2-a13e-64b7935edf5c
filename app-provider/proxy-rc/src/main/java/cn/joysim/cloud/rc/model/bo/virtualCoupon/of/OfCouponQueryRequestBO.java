package cn.joysim.cloud.rc.model.bo.virtualCoupon.of;

import lombok.Data;

@Data
public class OfCouponQueryRequestBO {

    /**
     * 为SP项目编码， 登陆api.ofpay.com创建，在欧飞商务审核通过后，会自动生成一个A开头的编号 如：A08566
     */
    private String userid;

    /**
     * 为项目密码的md5值(32位小写) 如md5(of111111) 新建项目在审核通过后系统会自动生成项目密码发送短信至创建人手机上
     */
    private String userpws;

    /**
     * Sp商家的订单号 唯一
     */
    private String sporderId;

    /**
     * 签名 规则参见1.3章节
     */
    private String md5Str;

    /**
     * 固定值 6.0
     */
    private String version;

    /**
     * 需要报文返回json格式时传
     */
    private String format;
}
