package cn.joysim.cloud.rc.model.bo.mobilePhone.ofpay;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:luo<PERSON>nhui
 * @create: 2023-04-17 14:18
 * @Description: 欧飞话费充值返回对象
 * <?xml version=\"1.0\" encoding=\"GB2312\" ?> <orderinfo>    <err_msg></err_msg>    <retcode>1</retcode>     	<orderid>S1803057559597</orderid>         <cardid>64349103</cardid>         <cardnum>1</cardnum>         <ordercash>500.5</ordercash>	        <cardname>全国中石油加油卡直充500元</cardname>         <sporder_id>a36d80682efd46799553c4572a551eed</sporder_id>         <game_userid>9030070000604712</game_userid>        <game_state>0</game_state></orderinfo>
 */
@Data
public class OfPayOrderResultBO implements Serializable {

    /**
     * 状态码
     */
    private Integer retcode;

    /**
     * 状态
     */
    private Integer game_state;

    /**
     * 订单号
     */
    private String orderid;

    /**
     *
     */
    private String ordercash;

    private String err_msg;

    /**
     * 充值卡号,油卡
     */
    private String chargeno;

    /**
     * 面值
     */
    private String parvalue;


    private String cardname;

    private String sporder_id;

    private String game_userid;


    private String cardid;
}