package cn.joysim.cloud.rc.model.bo.virtualCoupon.of;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

@Data
public class OfCouponCardBO {
    /**
     * 卡号
     */
    @JsonAlias({"cardno","cardNo"})
    private String cardNo;

    /**
     * 密码(字符串、短链)
     */
    @JsonAlias({"cardpws","cardPws"})
    private String cardPws;

    /**
     * 有效期
     */
    @JsonAlias({"expiretime","expiretime"})
    private String expireTime;
}