package cn.joysim.cloud.rc.model.bo.virtualCoupon.fl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:luo<PERSON>nhui
 * @create: 2024-03-12 16:31
 * @Description: 福禄直充下单返回结果
 */
@Data
public class FuLuDiRectOrderResponseBO implements Serializable {

    /**
     * 成功
     */
    public static final String SUCCESS_CODE = "success";

    /**
     * 处理中
     */
    public static final String PROCESSING = "processing";

    /**
     * 失败
     */
    public static final String FAILED = "failed";

    /**
     * 未处理
     */
    public static final String UNTREATED = "untreated";

    /**
     * 订单编号
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 外部订单号
     */
    @JsonProperty("customer_order_no")
    private String customerOrderNo;

    /**
     * 商品Id
     */
    @JsonProperty("product_id")
    private Long productId;

    /**
     * 商品名称
     */
    @JsonProperty("product_name")
    private String productName;

    /**
     * 充值账号
     */
    @JsonProperty("charge_account")
    private String chargeAccount;

    /**
     * 购买数量
     */
    @JsonProperty("buy_num")
    private Integer buyNum;

    /**
     * 订单类型：1-话费 2-流量 3-卡密 4-直充
     */
    @JsonProperty("order_type")
    private Integer orderType;

    /**
     * 交易单价
     */
    @JsonProperty("order_price")
    private Double orderPrice;

    /**
     * 订单状态： （success：成功，processing：处理中，failed：失败，untreated：未处理）
     */
    @JsonProperty("order_state")
    private String orderState;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 运营商流水号
     */
    @JsonProperty("operator_serial_number")
    private String operatorSerialNumber;
}