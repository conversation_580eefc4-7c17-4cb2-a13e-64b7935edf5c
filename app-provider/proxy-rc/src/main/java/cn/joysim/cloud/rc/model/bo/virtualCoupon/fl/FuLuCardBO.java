package cn.joysim.cloud.rc.model.bo.virtualCoupon.fl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author:l<PERSON><PERSON><PERSON>ui
 * @create: 2024-03-12 18:09
 * @Description: 福禄卡密信息
 */
@Data
public class FuLuCardBO implements Serializable {
    /**
     * 卡类型 0.普通卡密 1.二维码 2.短链。
     * 当商品类型是卡密时，卡类型只会是0.普通卡密，不会出现1.二维码 2.短链；只有当商品类型是直充时，才会出现这三种类型。
     */
    @JsonProperty("card_type")
    private Integer card_type;

    /**
     * 卡号
     */
    @JsonProperty("card_number")
    private String cardNumber;

    /**
     * 密码
     */
    @JsonProperty("card_pwd")
    private String cardPwd;

    /**
     * 卡密有效期
     */
    @JsonProperty("card_deadline")
    private String cardDeadline;

}