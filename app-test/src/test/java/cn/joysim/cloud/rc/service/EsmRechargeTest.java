package cn.joysim.cloud.rc.service;

import cn.joysim.cloud.JCloudBootStrapTest;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.model.bo.DoRechargeProxyCouponResultBO;
import cn.joysim.cloud.rc.model.dto.ChannelSupplierInfoDTO;
import cn.joysim.cloud.rc.model.dto.coupon.RechargeCouponApiDTO;
import cn.joysim.cloud.rc.service.impl.EsmSupplierProxyAccountServiceImpl;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;

public class EsmRechargeTest extends JCloudBootStrapTest {

    @Resource(name = EsmSupplierProxyAccountServiceImpl.ADAPTOR_NAME)
    private EsmSupplierProxyAccountServiceImpl esmSupplierProxyAccountService;

    @Test
    public void esmRechargeTest() {
        ChannelSupplierInfoDTO supplierInfo = new ChannelSupplierInfoDTO();
        supplierInfo.setSupplierConfig("{\"url\":\"http://files.joysim.cn:30123/equan/api\",\"city\":\"0\",\"days\":\"1\",\"appId\":\"jxtest\",\"months\":\"\",\"channel\":\"0\",\"sendWay\":\"1\",\"publicKey\":\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlOHjIExVB/IGcR68sAAJ6hRs6cRkhttiZgUp9d3npuAcJFd4ZDL1ZDa+Y1jGZGyPGxO+ole7UfBkHVftBYmTthrMHvT/fB4FhCk6V2xsLiuuY+1iyrhjbFFZdDkMNo2zjlw9YUbiIPXgoUTDdV3havnQwHIvgSUADhcEuPS/onAZ+FNj3vz4MkAgCovi2VXCWdZFvNoG2MBMvmphddhPiKSu/HgKbr30Xr+5ap2+UCyGD/MB2S/i8ThJIgklgA9hPVLjAC26DVs2JgdehuXuc5KaBkwXrk7COs8ivW8A0xPcMq5QK8aaIGhqIzoCKFBe58Brodbf8XzchkX1FshfIwIDAQAB\",\"secretKey\":\"11111111111111111111111111111111\",\"privateKey\":\"MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCU4eMgTFUH8gZxHrywAAnqFGzpxGSG22JmBSn13eem4BwkV3hkMvVkNr5jWMZkbI8bE76iV7tR8GQdV+0FiZO2Gswe9P98HgWEKTpXbGwuK65j7WLKuGNsUVl0OQw2jbOOXD1hRuIg9eChRMN1XeFq+dDAci+BJQAOFwS49L+icBn4U2Pe/PgyQCAKi+LZVcJZ1kW82gbYwEy+amF12E+IpK78eApuvfRev7lqnb5QLIYP8wHZL+LxOEkiCSWAD2E9UuMALboNWzYmB16G5e5zkpoGTBeuTsI6zyK9bwDTE9wyrlArxpogaGojOgIoUF7nwGuh1t/xfNyGRfUWyF8jAgMBAAECggEACTwNm3ZAm/KOJbwTVhNaihNkQge1eIvpCYzuclqmQLqL5FEsnYJvqByhKIZcH+R/590MqQMqEkYH364RfJNw/w2gdZcatbci/nSUMtH9z8tT0MzroMlZGWHeW5AiSsv1RDJCeGMuKX82xKoAOapuDfWa1iT+/1KtMzKIT3SW4ltbTfuXQhsZCtnIrazf3T4OsyYSQ5r6+mb9Dm5DOeT0U1Fe6ZrrE9pi0y5AGLgwr4SdQ6h4n8Yij5+rbt5z/ZHS2lIt2I2qxNkm0G2zZDv69E936ea+gvMp5iBg8bk+lC+/PhTdr9NaMyHGzobTWAVL+g+YCOvFde8MOVfml4gTmQKBgQDbn4qaUdl81ESBOuTjyWE088WQN39sbD9C55g7CFsMQOJlWMcsTCqbXFkusFhD+7EZaRZquJnh71zF6DDnerhJnkZBWiIq5F/PHMk4hCGYvwpP2b6z4pyqgVV68K0oNXq2msRYueJL/omU0XD9HyrJ8dL/sgydiAu4AtjznO6+RwKBgQCtistaqyfxo/X7a7yQN1IThqN7SbMckotGVVIj5hQD2H+UtYYgZp/GPPbNzLSbKMHostgJytOzLu8oYTZWlI+OQpj+7xDUNobqvyZayhCGTgrzvy72PFF+/OfcMKBS03xW6jexv8qb8OBJHUkvbb48xVYlTrVDoIO19vrlbJU6RQKBgGvK5FZVse9Ip87dvxXl8yXwN5lPA+WNQ4ds8BSZR0a+Wd/wMOGXCJp0OvllaA0F7ahRIcWDkVOJDfm8urQCUY0gd87/lCGFMVkQAdsRTwQPT9pdPvI/WTF+hRvwIKzJgs85KwF5So6OAazfFxvFPlUbsbn6A6MV8NqB20XGUo9lAoGACKiyONXtaksqDN91SyTXfY3sPkyRGU3ekSXUsWHbgc2doAfmp1s3gaatEduy70L7WecCjIqIQZqH6FXhZjSHbSu5go7fIheOZ8wd183ZQTpHpZ3WdAbScimgUAZFGZzfyV5qZP4S1/EO39cMPceWBGY8RCRHOxaZO80U+7mNQJkCgYEAqt9v/dV37DEYU9tE3jaYWK1XQVXXJiVQBzI6gLdZ5UZnPy4xJ5p98j/c5OpwKwgZrxWQRhyMR9qGniSn5y4urP1Oac/fYX5rgNah9dJzTlT/TDx0yZDSym3WNWG8Ok9I+PDMVaK4/+NEwijtG9haOttCLJ+ICeK5riDLuXmVjCw=\"}");

        RechargeCouponApiDTO apiDTO = new RechargeCouponApiDTO();
        apiDTO.setBatchCode("8");
        apiDTO.setFaceValue(BigDecimal.valueOf(50));
        apiDTO.setAccountNo("");
        apiDTO.setCouponId(1111111111111111111L);

        DoRechargeProxyCouponResultBO doRechargeProxyCouponResultBO = esmSupplierProxyAccountService.doRechargeCoupon(supplierInfo, apiDTO);
        Assert.assertNotNull(doRechargeProxyCouponResultBO);
        System.out.println(JSON.toJSONString(doRechargeProxyCouponResultBO));
    }
}
