package cn.joysim.cloud.rc.service;

import cn.hutool.core.util.StrUtil;
import cn.joysim.cloud.JCloudBootStrapTest;
import cn.joysim.cloud.rc.common.model.pojo.enums.CouponState;
import cn.joysim.cloud.rc.common.model.pojo.enums.RechargeKaChiState;
import cn.joysim.cloud.rc.common.model.pojo.enums.RechargeOrderState;
import cn.joysim.cloud.rc.common.model.pojo.enums.SpType;
import cn.joysim.cloud.rc.model.bo.CommonOrderUpdateStateBO;
import cn.joysim.cloud.rc.model.bo.DoRechargeBO;
import cn.joysim.cloud.rc.model.bo.DoRechargeResultBO;
import cn.joysim.cloud.rc.model.dto.RechargeCouponDTO;
import cn.joysim.cloud.rc.model.dto.RechargeOrderMainDTO;
import cn.joysim.cloud.rc.service.impl.virtualCoupon.RechargeCouponCallbackServiceImpl;
import cn.joysim.cloud.rc.service.mobilePhone.RechargeOrderMainService;
import cn.joysim.cloud.rc.service.virtualCoupon.RechargeCouponCallbackService;
import cn.joysim.cloud.rc.utils.CheckPhone;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.databind.json.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 充值服务单元测试
 */
@Slf4j
public class RechargeServiceTest extends JCloudBootStrapTest {

    @Resource
    private RechargeService rechargeService;

    private static final String BUSINESS_CODE = "proxy_junit_test";

    @Resource
    private RechargeOrderMainService rechargeOrderInfoService;

    @Resource
    private RechargeCouponCallbackService rechargeCouponCallbackService;


    @Test
    public void doRecharge() {
        DoRechargeBO doRechargeBO = new DoRechargeBO();
        doRechargeBO.setBusinessCode(BUSINESS_CODE);
        doRechargeBO.setCustomBizId(IdWorker.getIdStr());
        doRechargeBO.setDenomination(new BigDecimal("1.00"));
        doRechargeBO.setPhoneNo("***********");
        doRechargeBO.setCallbackUrl("https://joysim.cn");
        DoRechargeResultBO resultBO = rechargeService.doRecharge(doRechargeBO);
        log.info("result = {}", resultBO);
    }

    @Test
    public void mobileNoRegTest() {
        List<String> cmccNoList = new ArrayList<>();

        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("***********");
        cmccNoList.add("16588888888");
        cmccNoList.add("17288888888");
        cmccNoList.add("17888888888");
        cmccNoList.add("18288888888");
        cmccNoList.add("18388888888");
        cmccNoList.add("18488888888");
        cmccNoList.add("18788888888");
        cmccNoList.add("18888888888");
        cmccNoList.add("19588888888");
        cmccNoList.add("19788888888");
        cmccNoList.add("19888888888");

        cmccNoList.add("17038888888");
        cmccNoList.add("17058888888");
        cmccNoList.add("17068888888");

        List<String> cuccNoList = new ArrayList<>();
        cuccNoList.add("13088888888");
        cuccNoList.add("13188888888");
        cuccNoList.add("13288888888");
        cuccNoList.add("14088888888");
        cuccNoList.add("14588888888");
        cuccNoList.add("14688888888");
        cuccNoList.add("15588888888");
        cuccNoList.add("15688888888");
        cuccNoList.add("16688888888");
        cuccNoList.add("16788888888");
        cuccNoList.add("17188888888");
        cuccNoList.add("17588888888");
        cuccNoList.add("17688888888");
        cuccNoList.add("18588888888");
        cuccNoList.add("18688888888");
        cuccNoList.add("19688888888");

        cuccNoList.add("17048888888");
        cuccNoList.add("17078888888");
        cuccNoList.add("17088888888");
        cuccNoList.add("17098888888");

        List<String> ctccNoList = new ArrayList<>();
        ctccNoList.add("13388888888");
        ctccNoList.add("14988888888");
        ctccNoList.add("15388888888");
        ctccNoList.add("16288888888");
        ctccNoList.add("17388888888");
        ctccNoList.add("17788888888");
        ctccNoList.add("18088888888");
        ctccNoList.add("18188888888");
        ctccNoList.add("18988888888");
        ctccNoList.add("19088888888");
        ctccNoList.add("19188888888");
        ctccNoList.add("19388888888");
        ctccNoList.add("19988888888");

        ctccNoList.add("17008888888");
        ctccNoList.add("17018888888");
        ctccNoList.add("17028888888");

        List<String> errorNoList = new ArrayList<>();
        // 移动号段
        cmccNoList.forEach(no -> {
            try {
                SpType type = CheckPhone.checkPhone(no);
                if (!SpType.CMCC.equals(type)) {
                    errorNoList.add(no + ":" + type.getText());
                }
            } catch (Exception e) {
                errorNoList.add(no + ":unKnown");
            }
        });

        // 联通号段
        cuccNoList.forEach(no -> {
            try {
                SpType type = CheckPhone.checkPhone(no);
                if (!SpType.CUCC.equals(type)) {
                    errorNoList.add(no + ":" + type.getText());
                }
            } catch (Exception e) {
                errorNoList.add(no + ":unKnown");
            }
        });

        // 电信号段
        ctccNoList.forEach(no -> {
            try {
                SpType type = CheckPhone.checkPhone(no);
                if (!SpType.CTCC.equals(type)) {
                    errorNoList.add(no + ":" + type.getText());
                }
            } catch (Exception e) {
                errorNoList.add(no + ":unKnown");
            }
        });

        log.info("errorNoList = {}", errorNoList);
        Assert.assertEquals(0, errorNoList.size());
    }

    @Test
    public void test3(){
        boolean updateResult;
        RechargeOrderMainDTO orderInfo = rechargeOrderInfoService.getById(1647046079450882050L);
        String resultCode = "T00004";
        Long orderId = orderInfo.getId();
        if (RechargeKaChiState.RECHARGE_SUCCESS.getCode().equals(resultCode)) {
            //充值成功
            CommonOrderUpdateStateBO updateStateBO = null;
            if (RechargeOrderState.WAITING_REVIEW.equals(orderInfo.getOrderState())){
                log.info("订单号：{};订单状态：{};修改状态为成功！！！",orderInfo.getId(),orderInfo.getOrderState().getText());
                updateStateBO = new CommonOrderUpdateStateBO(orderId, RechargeOrderState.WAITING_REVIEW, RechargeOrderState.RECHARGE_SUCCESS);
            }else {
                updateStateBO = new CommonOrderUpdateStateBO(orderId, RechargeOrderState.RECHARGING, RechargeOrderState.RECHARGE_SUCCESS);
            }
            if (StrUtil.isBlank(orderInfo.getSupplierBizId())) {
                updateStateBO.setSupplierBizId("123456");
            }
            updateResult = rechargeOrderInfoService.updateOrderState(updateStateBO);
        } else if (RechargeKaChiState.RECHARGE_FAIL.getCode().equals(resultCode)) {
            //充值失败
            CommonOrderUpdateStateBO updateStateBO = null;
            if (RechargeOrderState.WAITING_REVIEW.equals(orderInfo.getOrderState())) {
                log.info("订单号：{};订单状态：{};修改状态为失败！！！", orderInfo.getId(), orderInfo.getOrderState().getText());
                updateStateBO = new CommonOrderUpdateStateBO(orderId, RechargeOrderState.WAITING_REVIEW, RechargeOrderState.RECHARGE_FAILED);
            } else {
                updateStateBO = new CommonOrderUpdateStateBO(orderId, RechargeOrderState.RECHARGING, RechargeOrderState.RECHARGE_FAILED);
            }

            if (StrUtil.isBlank(orderInfo.getSupplierBizId())) {
                updateStateBO.setSupplierBizId("456789");
            }
            updateResult = rechargeOrderInfoService.updateOrderState(updateStateBO);
        }
    }

    @Test
    public void testCouponSendSuccess(){
        RechargeCouponDTO rechargeCouponDTO = new RechargeCouponDTO();
        rechargeCouponDTO.setId(1711702783245037570L);
        rechargeCouponDTO.setCouponOrderId(1711702783106625538L);
        rechargeCouponDTO.setState(CouponState.SEND);
        rechargeCouponDTO.setCouponName("腾讯超级影视会员（季卡）");
        rechargeCouponDTO.setAmount(BigDecimal.valueOf(79.00));
        rechargeCouponCallbackService.couponSendSuccess(rechargeCouponDTO);
    }
}
