package cn.joysim.cloud.rc.service;

import cn.joysim.cloud.JCloudBootStrapTest;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.util.IdUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.common.model.pojo.enums.RechargeProductType;
import cn.joysim.cloud.rc.model.bo.*;
import cn.joysim.cloud.rc.model.bo.qingyi.QingYiOrderResultBO;
import cn.joysim.cloud.rc.model.dto.ChannelProxyInfoDTO;
import cn.joysim.cloud.rc.model.dto.ChannelProxyItemInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class GeneralRechargeTest extends JCloudBootStrapTest {

    @Resource
    private RechargeService rechargeService;

    @Resource
    private RsRechargeService rsRechargeService;

    @Resource
    private ChannelProxyInfoService channelProxyInfoService;

    @Resource
    private ChannelProxyItemInfoService proxyItemInfoService;


    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private GeneralRechargeOrderService generalRechargeOrderService;



    
    @Test
    public void test(){
        //ProxyDoRechargeCommonRequest(businessCode=null,
        // productType=1, phoneNo=null,
        // oilCardNo=1000114400029691487,
        // receivePhoneNo=null,
        // accountNo=null,
        // accountInfo=null,
        // accountType=null,
        // denomination=100,
        // customBizId=oil1822526910504009730,
        // callbackUrl=http://equan.yesm.cn/equan-wxweb/eshop/jxRechargeCenter/oilCard/notify,
        // productId=null, spType=null, settlementPrice=null, region=null, receiveMessagePhoneNo=null, userName=null, userId=null)
        DoRechargeBO doRechargeBO = new DoRechargeBO();
        doRechargeBO.setProductType(1);
        doRechargeBO.setOilCardNo("****************");
        doRechargeBO.setDenomination(BigDecimal.valueOf(150));
        doRechargeBO.setCustomBizId(IdUtil.getIdStr());
        doRechargeBO.setIpAddress("*************");
        doRechargeBO.setBusinessCode("proxy_bmmall");
        doRechargeBO.setReceiveMessagePhoneNo("***********");
        doRechargeBO.setProductId("BMSC_ZCB_150_0814");
        doRechargeBO.setCallbackUrl("http://t6.yesm.cn/equan-wxweb/eshop/jxRechargeCenter/oilCard/notify");

        rechargeService.doRecharge(doRechargeBO);
    }

    
    @Test
    public void rstest(){
        //ProxyDoRechargeCommonRequest(businessCode=null,
        // productType=1, phoneNo=null,
        // oilCardNo=1000114400029691487,
        // receivePhoneNo=null,
        // accountNo=null,
        // accountInfo=null,
        // accountType=null,
        // denomination=100,
        // customBizId=oil1822526910504009730,
        // callbackUrl=http://equan.yesm.cn/equan-wxweb/eshop/jxRechargeCenter/oilCard/notify,
        // productId=null, spType=null, settlementPrice=null, region=null, receiveMessagePhoneNo=null, userName=null, userId=null)
        RsDirectChargeBO rechargeBO = new RsDirectChargeBO();
        rechargeBO.setBusinessCode("proxy_ylshshrs");
        rechargeBO.setSipOrderNo("202405081119U6836812");
        rechargeBO.setRechargeTag("RS_ZCB_150_0814");
        rechargeBO.setAccountType(6);
        rechargeBO.setAccountNo("****************");
        rechargeBO.setNum(1);



        ChannelProxyInfoDTO proxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(rechargeBO.getBusinessCode());
        ChannelProxyItemInfoDTO proxyItemInfoDTO = proxyItemInfoService.getByProductId(proxyInfoDTO.getId(),rechargeBO.getRechargeTag());
        RsDirectChargeResultBO rsRechargeResultBO = rsRechargeService.rsDirectRecharge(rechargeBO, proxyInfoDTO);
        rsRechargeService.rsDirectRecharge(rechargeBO,proxyInfoDTO);
    }



    
    @Test
    public void mockQingyiSuccessCallback(){

        QingYiOrderResultBO qingYiOrderResultBO = new QingYiOrderResultBO();
        qingYiOrderResultBO.setOrder_id("");
        qingYiOrderResultBO.setSerial_number(IdUtil.getIdStr());
        qingYiOrderResultBO.setPrice("100");
        qingYiOrderResultBO.setDelivery_state("1");



        boolean result = rechargeService.rechargeCallbackForBJQYForGeneral(qingYiOrderResultBO);
    }

    
    @Test
    public void mockQingyiFailCallback(){

        QingYiOrderResultBO qingYiOrderResultBO = new QingYiOrderResultBO();
        qingYiOrderResultBO.setOrder_id("");
        qingYiOrderResultBO.setSerial_number(IdUtil.getIdStr());
        qingYiOrderResultBO.setPrice("100");
        qingYiOrderResultBO.setDelivery_state("2");


        boolean result = rechargeService.rechargeCallbackForBJQYForGeneral(qingYiOrderResultBO);
    }

    
    @Test
    public void queryXXl(){
        XxlJobBo xxlJobBo = new XxlJobBo();
        xxlJobBo.setHandler("queryGeneralRechargeJob");
        xxlJobBo.setParams(null);

        xxlJobService.executeXxlJob(xxlJobBo);
    }

    
    @Test
    public void fini(){
        generalRechargeOrderService.finalizeOrderIfAllDetailsComplete();
    }



    
    @Test
    public void query(){
        List<RCRechargeOrderInfoBO> orderList = rechargeService
                .query("proxy_bmmall", Arrays.asList("1826131436085432322"), RechargeProductType.RECHARGE_OIL_CARD);
        log.info("结果:{}", JSON.toJSONString(orderList));
    }

}
