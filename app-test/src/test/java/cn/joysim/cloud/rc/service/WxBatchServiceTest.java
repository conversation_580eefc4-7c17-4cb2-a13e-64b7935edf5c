package cn.joysim.cloud.rc.service;

import cn.joysim.cloud.JCloudBootStrapTest;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.model.bo.weChat.SupplierWeChatConfigBO;
import com.github.binarywang.wxpay.bean.marketing.FavorStocksCreateRequest;
import com.github.binarywang.wxpay.bean.marketing.FavorStocksCreateResult;
import com.github.binarywang.wxpay.bean.marketing.FavorStocksGetResult;
import com.github.binarywang.wxpay.bean.marketing.enums.StockTypeEnum;
import com.github.binarywang.wxpay.bean.marketing.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.bean.media.ImageUploadResult;
import com.github.binarywang.wxpay.bean.media.MarketingImageUploadResult;
import com.github.binarywang.wxpay.service.MarketingFavorService;
import com.github.binarywang.wxpay.service.MarketingMediaService;
import com.github.binarywang.wxpay.service.MerchantMediaService;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.SneakyThrows;
import org.junit.Test;

import javax.annotation.Resource;
import java.io.File;
import java.util.Arrays;

public class WxBatchServiceTest extends JCloudBootStrapTest {


    @Resource
    private RCWxPayService rcWxPayService;

    @SneakyThrows
    @Test
    public void test() {
        String config = "{\n" +
                "  \"mchId\": \"1606877998\",\n" +
                "  \"mchKey\": \"9f7928d29670443fb785ce92e553d472\",\n" +
                "  \"certPath\": \"/home/<USER>/softs/j-cloud-saas-recharge-center/conf/piclient_cert.p12\",\n" +
                "  \"privateKey\": \"\",\n" +
                "  \"mchSerialNo\": \"\",\n" +
                "  \"supportAppIds\": \"wx98a1f752aef8bc8f,wx770222ffed8d7b75,wx1e38a27a5dc39a90,wx36f7961d19020e8b,wxa4741a87254704d3,wx0db6dce66249f815\",\n" +
                "  \"privateKeyPath\": \"/home/<USER>/softs/j-cloud-saas-recharge-center/conf/apiclient_key.pem\",\n" +
                "  \"privateCertPath\": \"/home/<USER>/softs/j-cloud-saas-recharge-center/conf/apiclient_cert.pem\",\n" +
                "  \"stockSenderMchId\": \"1606877998\",\n" +
                "  \"wechatApiV3CertPath\": \"/home/<USER>/softs/j-cloud-saas-recharge-center/conf/piclient_cert.p12\",\n" +
                "  \"wechatApiV3SecretKey\": \"d5ed4bab3d9f49119c9ae66ed0895f7f\"\n" +
                "}";
        //微信商户号配置
        SupplierWeChatConfigBO configBO = JSON.parse(config, SupplierWeChatConfigBO.class);
        //获取微信服务
        WxPayService wxPayService = rcWxPayService.getWxPayService(configBO);

//        //获取发券服务
        MarketingFavorService marketingFavorService = wxPayService.getMarketingFavorService();
//        MarketingMediaService marketingMediaService = wxPayService.getMarketingMediaService();
//        MarketingImageUploadResult marketingImageUploadResult = marketingMediaService.imageUploadV3(new File("Z:\\图片\\底图.png"));
//        System.out.println(    marketingImageUploadResult);


        FavorStocksCreateRequest.FixedNormalCoupon fixedNormalCoupon = new FavorStocksCreateRequest.FixedNormalCoupon();
        fixedNormalCoupon.setCouponAmount(1000);
        fixedNormalCoupon.setTransactionMinimum(1001);


        FavorStocksCreateRequest.CouponUseRule couponUseRule = new FavorStocksCreateRequest.CouponUseRule();
        couponUseRule.setFixedNormalCoupon(fixedNormalCoupon);
        //couponUseRule.setGoodsTag();
        //couponUseRule.setLimitPay(Arrays.asList("ICBC_CREDIT"));
        //couponUseRule.setLimitCard();
        couponUseRule.setTradeType(Arrays.asList(TradeTypeEnum.MICROAPP));
        couponUseRule.setCombineUse(Boolean.TRUE);
        //couponUseRule.setAvailableItems();
        couponUseRule.setAvailableMerchants(Arrays.asList("1606877998"));


        FavorStocksCreateRequest.StockUseRule stockUseRule = new FavorStocksCreateRequest.StockUseRule();
        stockUseRule.setMaxCoupons(100);
        stockUseRule.setMaxAmount(100000);
        stockUseRule.setMaxAmountByDay(10000);
        stockUseRule.setMaxCouponsPerUser(3);
        stockUseRule.setNaturalPersonLimit(Boolean.TRUE);
        stockUseRule.setPreventApiAbuse(Boolean.TRUE);


        FavorStocksCreateRequest.PatternInfo patternInfo = new FavorStocksCreateRequest.PatternInfo();
        patternInfo.setDescription("（1）中国银行微信立减金5元，微信支付交易需满5.01元方能享受立减5元的优惠，每笔仅可使用一张立减金，不可叠加使用。");
        //patternInfo.setMerchantLogo();
        //patternInfo.setMerchantName("");
        //patternInfo.setBackgroundColor();
        //patternInfo.setCouponImage("https://wxpaylogo.qpic.cn/wxpaylogo/PiajxSqBRaEKcYsiccVkic1RnIDUcPJFG9VoPN3BE30ibrBvNFv1ChKicCg/0");


        FavorStocksCreateRequest favorStocksCreateRequest = new FavorStocksCreateRequest();
        favorStocksCreateRequest.setStockName("中国银行代金券");
        favorStocksCreateRequest.setComment("测试API接口");
        favorStocksCreateRequest.setBelongMerchant("1606877998");
        favorStocksCreateRequest.setAvailableBeginTime("2024-01-04T00:00:00.000+08:00");
        favorStocksCreateRequest.setAvailableEndTime("2024-01-30T00:00:00.000+08:00");
        favorStocksCreateRequest.setStockUseRule(stockUseRule);
        favorStocksCreateRequest.setPatternInfo(patternInfo);
        favorStocksCreateRequest.setCouponUseRule(couponUseRule);
        favorStocksCreateRequest.setNoCash(Boolean.TRUE);
        favorStocksCreateRequest.setStockType(StockTypeEnum.NORMAL);
        favorStocksCreateRequest.setOutRequestNo("89560002019101000121");
        //favorStocksCreateRequest.setExtInfo();

        FavorStocksCreateResult favorStocksV3 = marketingFavorService.createFavorStocksV3(favorStocksCreateRequest);
        System.out.println(favorStocksV3);
    }

}
