package cn.joysim.cloud.rc.service;

import cn.hutool.core.date.DateUtil;

import cn.joysim.cloud.rc.model.bo.virtualCoupon.unionpay.bo.OrderSendTicketReq;
import cn.joysim.cloud.rc.utils.DaXiaoUtil;

import java.util.Date;

public class DaXiaoRechargeTest {

    private static final String UNIONPAY_PUBLIC_KEY = "0413605efc2fb79b3f4493e3085ec42ffb78647ab1310069c0cd5d69410b6c45caa9aa6829a64049a95bd78f74d67712b50f12b00d7269579639c4e342fa68acf8";
    //private static final String UNIONPAY_PUBLIC_KEY = "0413605efc2fb79b3f4493e3085ec42ffb78647ab1310069c0cd5d69410b6c45caa9aa6829a64049a95bd78f74d67712b50f12b00d7269579639c4e342fa68acf9";
    private static final String JOYSIM_SM2_PUBLIC_KEY = "04305e6eb5d3ea3b987e7864908f3655bfa4f1dbd2a4612b62892ff8967a12be285e877bc50158cf4ab5018a87e4f35243ee1a571837a3ebf5d7bb3f0be5d5683d";
    private static final String JOYSIM_SM2_PRIVATE_KEY = "00c49f7c333b9ec8a9cc9adc450785299419f642babf31408b9f1619c264ecacbd";
    //private static final String JOYSIM_SM2_PRIVATE_KEY = "00c49f7c333b9ec8a9cc9adc450785299419f642babf31408b9f1619c264ecacbe";

    private static final String TICKET_URL = "http://dev.spserv.yxlm.chinaums.com:25941/spapigateway/v2/markting/sp/coupon/order/get";
    //private static final String TICKET_URL = "http://dev.spserv.yxlm.chinaums.com:25941/spapigateway/v2/markting/sp/unionpay/redpacket/order";
    private static final String ACCOUNT_BALANCE = "http://dev.spserv.yxlm.chinaums.com:25941/spapigateway/v2/markting/sp/unionpay/redpacket/balance";


    public static void main(String[] args) {
        String sendTicketUrl = TICKET_URL;
        String pub = UNIONPAY_PUBLIC_KEY;
        String pri = JOYSIM_SM2_PRIVATE_KEY;
        OrderSendTicketReq req = new OrderSendTicketReq();

        req.setMsgType("10");
        req.setMsgTxnCode("106040");
        req.setMsgCrrltnId("*****************");
        req.setMsgFlg("0");
        req.setMsgSender("660238");
        req.setMsgTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        req.setMsgSysSn("*****************");
        req.setMsgVer("0.2");
        req.setSignType("SM2");
        req.setSpChnlNo("660238");
        req.setSpOrderNo("ysf10000000000011");
        req.setOrderDate(DateUtil.format(new Date(), "yyyyMMdd"));
        req.setEventNo("****************");
        req.setMobileNo("***********");
        req.setBuyQuantity(1);
        req.setNotifyUrl("https://t6.joysim.cn");
        String result = DaXiaoUtil.sendTicket(sendTicketUrl, req,pub ,pri);
        System.out.println(result);
    }
}
