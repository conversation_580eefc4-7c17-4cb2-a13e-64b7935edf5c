package cn.joysim.cloud.rc.service;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.controller.request.RsrsDirectChargeRequest;
import cn.joysim.cloud.rc.model.bo.RsDirectChargeBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * 充值管理接口
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class RsRechargeControllerTest {


    public static void main(String[] args) {


        RsDirectChargeBO rsDirectChargeBO = new RsDirectChargeBO();
        rsDirectChargeBO.setSipOrderNo("1231212212121222222");
        rsDirectChargeBO.setRechargeTag("rs_oil_100");
        rsDirectChargeBO.setAccountType(6);
        rsDirectChargeBO.setAccountNo("10001100000000000024444");
        rsDirectChargeBO.setNum(1);



        String post = HttpUtil.post("http://127.0.0.1:11001/api/rc/recharge/encrypt?vendorNo=proxy_ylshshrs_test", JSON.toJSONString(rsDirectChargeBO));
        System.out.println(post);


//        RSAPublicKey publicKey = RSAHelper.getPemRSAPublicKey(
//                "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDHBIStwZtFlJ9sWvX/AxunHeUBJ9KhfO7CAFcR0NDGwbhMnKz+MvaLGlZ77Z7ek9YzpiwDuDNaBMycz6UQ/An5Cohq7i7tW3bIfnQEQm2MNRQ0lP+asmHRuucDriSBRg8H+utbI52CCrD8DIm/vHnxDRrn184wctyN89pmypdqgwIDAQAB");
//
//
//        RSAPrivateKey privateKey = RSAHelper.getPemRSAPrivateKey("MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIc9PErLzL0w4YjaHnYO5W+TRGU6/Y2MFNjzx+scuTepFhTbjKZ0ZXgf761rwNmIwk1rU4jWgDJgxWULw5loQ1ZOdQ8nYS3vqOdDXN22ebV+XvmXzredjInH6onCiEFOogca1CkunqlkctYtiDoHtRiSmdrtM5OMSkqZWinqo7ZxAgMBAAECgYAJS1QRb+AqQGm/lf3x7yQlfuF8c4lpaO8l2dsrdVXlVWGHHW+VIPq4PrmiGX2vdY6k83NSPCujSrNGNoDC0j+OXM6hCmE3FBzws7K0Bt7PnS3f/1l2Rsx8aGDq+XpJEhUICx/v5GfXeqmTgm/0HmgEcZO/QfRI3LpRkEsMkx8OgQJBAONjWcgvyP2uWLmvZlfawWgLbusG0buxXH+AOFHwDg3TtWPi7boRS6IRzOFO/iya6+UZc3UVQMkdzcC8SHjZRGUCQQCYQZM2JB/7TJXG3pccEokBGQuDxjbOJZEKuifs4qCW2nm4Trr7KRwjoFgwSiPZgMa7li7C8ZoYBe9IqspbwisdAkBG6XbYXT6bDkIRNkf+YfQq1FX06Z2CoR8ti/kEZI6ddUZ+LgjhG/+wPUjdgtr1YiLqwXMmMkiP78F4t78KIQeZAkEAjXvCNHl1TqxoMhj+CpQew+pmDNnQa9f05CcAmtwtpoD2wxJGJsaY43JJAPakQaWtBvwUEAs6ykAZj0lGKeZQ8QJABb7ocsVGdLlPCe/2oKdUuWCDS4U72DmLP1xz20nFzfYAuuch4WTz+8cYW8Gb0HvWLBkij8ov3aLiYXZnddBFPA==");
//
//        String data = JSONUtil.toJsonStr(rsDirectChargeBO);
//        Map<String, Object> params = JSONObject.parseObject(data, Map.class);
//        Map<String, Object> sortedMap = params.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
//        String rsaData = RSAHelper.encrypt(publicKey, JSONUtil.toJsonStr(sortedMap));
//
//        String sign = RSAHelper.sign(JSONUtil.toJsonStr(sortedMap), privateKey);

        cn.hutool.json.JSON json = JSONUtil.parse(post);
        String rsaData = json.getByPath("data.data", String.class);
        String sign = json.getByPath("data.sign", String.class);
        System.out.println(rsaData);
        System.out.println(sign);

        RsrsDirectChargeRequest rsrsDirectChargeRequest = new RsrsDirectChargeRequest();
        rsrsDirectChargeRequest.setVendorNo("proxy_ylshshrs");
        rsrsDirectChargeRequest.setData(rsaData);
        rsrsDirectChargeRequest.setSign(sign);


        String post2 = HttpUtil.post("http://127.0.0.1:11001/api/rc/recharge/rsDirectCharge", JSON.toJSONString(rsrsDirectChargeRequest));
        System.out.println(post2);


    }
}
