package cn.joysim.cloud.rc.service;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.JCloudBootStrapTest;
import cn.joysim.cloud.rc.controller.request.ProxyExpressQueryRequest;

import javax.annotation.Resource;

/**
 * @description:
 * @Author: LiuHW
 * @date: 2023/8/31 11:54
 */
public class ExpressQueryServiceTest extends JCloudBootStrapTest {

    @Resource
    private ExpressOrderService expressOrderService;

    public static void main(String[] args) {
        encryptJsonStr();
    }

    public static String encryptJsonStr() {
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClQKh99djLX5nTGzREcdfg6Pad1TIxWQtfUa5k84kcKyM+5a0xLsEGgXMKNZRXLQ5u05Tna3/SslbvOfM508kJz4cUt9xyhaFXmmNgvsU3eOtY2zvnrX1DMxF7tuLeSiO8lgpQdADau7XbGZpYGjHf8iTW5TyR/pA4/kJlJQtd3QIDAQAB";

        ProxyExpressQueryRequest request = new ProxyExpressQueryRequest();
        request.setNumber("78363345509181");
        request.setMobile("13288996845");
        // ⽤公钥加密数据
        String encryptStr = SecureUtil.rsa(null, publicKey).encryptBase64(JSONUtil.toJsonStr(request), KeyType.PublicKey);
        System.out.println("encryptStr = " + encryptStr);
        return encryptStr;
    }
}
