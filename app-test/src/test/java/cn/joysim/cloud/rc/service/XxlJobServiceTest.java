package cn.joysim.cloud.rc.service;

import cn.joysim.cloud.JCloudBootStrapTest;
import cn.joysim.cloud.rc.model.bo.XxlJobBo;
import org.junit.Test;

import javax.annotation.Resource;

public class XxlJobServiceTest extends JCloudBootStrapTest {


    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private LazyJob10Service lazyJob10Service;


    @Test
    public void test() {
        XxlJobBo xxlJobBo = new XxlJobBo();
        xxlJobBo.setHandler("aliBillDownloadJobHandler");
        xxlJobBo.setParams("");
        xxlJobService.executeXxlJob(xxlJobBo);
    }

    @Test
    public void test2() {
        XxlJobBo xxlJobBo = new XxlJobBo();
        xxlJobBo.setHandler("lazyJob10");
        xxlJobBo.setParams("");
        xxlJobService.executeXxlJob(xxlJobBo);
    }

}
