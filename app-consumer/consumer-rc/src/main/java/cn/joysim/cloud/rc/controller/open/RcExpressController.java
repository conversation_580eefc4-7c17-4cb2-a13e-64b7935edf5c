package cn.joysim.cloud.rc.controller.open;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Decrypt;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.controller.BaseController;
import cn.joysim.cloud.common.model.dto.BaseSecurityProtocol;
import cn.joysim.cloud.common.model.vo.BaseResponse;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.rc.controller.request.ProxyExpressQueryRequest;
import cn.joysim.cloud.rc.model.bo.express.QueryExpressBO;
import cn.joysim.cloud.rc.model.bo.express.QueryExpressProxyResultBO;
import cn.joysim.cloud.rc.service.ExpressOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.Set;

/**
 * @description: 快递物流
 * @Author: LiuHW
 * @date: 2023/8/30 11:29
 */
@RestController
@RequestMapping("/api/rc/express")
@Slf4j
public class RcExpressController extends BaseController {

    @Resource
    private ExpressOrderService expressOrderService;

    @Resource
    private Validator validator;

    /**
     * 快递物流查询
     *
     * @param decryptData
     * @param request
     * @return
     */
    @PostMapping("/query")
    @Permission(action = Action.Skip)
    public BaseResponse query(@Decrypt BaseSecurityProtocol decryptData, HttpServletRequest request) {
        BaseResponse response = new BaseResponse();
        log.info("快递物流查询参数：{}", JSONUtil.toJsonStr(decryptData));
        ProxyExpressQueryRequest queryRequest = BeanUtil.toBean(decryptData.getPlainTextObj(), ProxyExpressQueryRequest.class);

        //1.校验参数
        StringBuilder msg = validate(queryRequest);
        if (StringUtils.hasText(msg.toString())) {
            response.setFailed();
            response.setMsg(msg.toString());
            return response;
        }

        //2.查询快递
        QueryExpressBO query = new QueryExpressBO();
        BeanUtil.copyProperties(queryRequest, query);
        query.setBusinessCode(decryptData.getBusinessCode());
        query.setIpAddress(IpAddrUtil.getRealIp(request));
        query.setNumber(query.getNumber().replaceAll("\u200B", "").trim());
        QueryExpressProxyResultBO result = expressOrderService.query(query);
        response = BeanUtil.toBean(result, BaseResponse.class);
        return response;
    }

    public StringBuilder validate(ProxyExpressQueryRequest request) {
        StringBuilder msg = new StringBuilder();
        Set<ConstraintViolation<ProxyExpressQueryRequest>> violationSet = validator.validate(request);
        for (ConstraintViolation<ProxyExpressQueryRequest> model : violationSet) {
            msg.append("[").append(model.getMessage()).append("]");
        }
        return msg;
    }
}
