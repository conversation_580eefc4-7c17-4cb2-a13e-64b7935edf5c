package cn.joysim.cloud.rc.controller.open;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SignAlgorithm;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.annotation.Token;
import cn.joysim.cloud.common.controller.BaseController;
import cn.joysim.cloud.common.model.dto.BaseSecurityProtocol;
import cn.joysim.cloud.common.model.vo.BaseResponse;
import cn.joysim.cloud.common.util.IdUtil;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.common.util.ResponseUtil;
import cn.joysim.cloud.psc.common.model.bo.PSCSecuritySignBO;
import cn.joysim.cloud.psc.common.model.bo.PSCSystemConfigBO;
import cn.joysim.cloud.psc.service.PSCEncryptOpenService;
import cn.joysim.cloud.psc.service.PSCSystemConfigOpenService;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.common.consts.RCRegExConst;
import cn.joysim.cloud.rc.common.exception.ChannelProxyInfoException;
import cn.joysim.cloud.rc.common.exception.ChannelSupplierException;
import cn.joysim.cloud.rc.common.exception.RsRechargeException;
import cn.joysim.cloud.rc.common.exception.status.RsRechargeStatusCode;
import cn.joysim.cloud.rc.common.model.pojo.enums.RCRequestLogType;
import cn.joysim.cloud.rc.common.model.pojo.enums.RsDirectChargeAccountTypeEnum;
import cn.joysim.cloud.rc.common.utils.IpWhite;
import cn.joysim.cloud.rc.controller.request.*;
import cn.joysim.cloud.rc.model.bo.*;
import cn.joysim.cloud.rc.model.dto.*;
import cn.joysim.cloud.rc.model.dto.request.BaseValidateRequest;
import cn.joysim.cloud.rc.service.ChannelProxyInfoService;
import cn.joysim.cloud.rc.service.ChannelProxyItemInfoService;
import cn.joysim.cloud.rc.service.RsRechargeService;
import cn.joysim.cloud.rc.service.cardCipher.RechargeCardCipherOrderService;
import cn.joysim.cloud.rc.service.proxy.ChannelProxyAbnormalRechargeRecordService;
import cn.joysim.cloud.rc.service.virtualCoupon.RechargeCouponService;
import cn.joysim.cloud.rc.service.virtualCoupon.RechargeVirtualCouponOrderService;
import cn.joysim.cloud.rc.service.virtualCoupon.RepushOrderLogService;
import cn.joysim.cloud.upc.common.model.bo.TokenInfo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validator;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 充值管理接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/rc/recharge")
@Slf4j
public class RsRechargeController extends BaseController {


    @Resource
    private RsRechargeService rsRechargeService;

    @Resource
    private ChannelProxyInfoService proxyInfoService;

    @Resource
    private ChannelProxyItemInfoService proxyItemInfoService;

    @Resource
    private Validator validator;

    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCSystemConfigOpenService configService;

    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCEncryptOpenService encryptOpenService;

    @Value("${common.application.id}")
    private Long sysId;
    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCSystemConfigOpenService pscSystemConfigOpenService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RechargeVirtualCouponOrderService virtualCouponOrderService;

    @Resource
    private RepushOrderLogService repushOrderLogService;

    @Resource
    private ChannelProxyAbnormalRechargeRecordService abnormalRechargeRecordService;

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;

    @Resource
    private RechargeVirtualCouponOrderService rechargeVirtualCouponOrderService;

    @Resource
    private RechargeCardCipherOrderService rechargeCardCipherOrderService;

    @Resource
    private RechargeCouponService rechargeCouponService;

    public void commonValidate(BaseValidateRequest validateRequest, StringBuilder errorSb) {
        Set<ConstraintViolation<BaseValidateRequest>> violationSet = validator.validate(validateRequest);
        for (ConstraintViolation<BaseValidateRequest> model : violationSet) {
            errorSb.append("[").append(model.getMessage()).append("]");
        }
    }

    @PostMapping("/rsRecharge")
    @Permission(action = Action.Skip)
    public RsRechargeResultBO rsRecharge(@Valid @RequestBody RsRechargeRequest recharge, HttpServletRequest request) {
        long time = System.currentTimeMillis();
        log.info("荣数发券下单参数:{}", JSONUtil.toJsonStr(recharge));
        String businessCode = recharge.getVendorNo();
        RsRechargeResultBO result = new RsRechargeResultBO();
        RsRechargeBO rechargeBO = null;
        try {
            //1.解密
            BaseSecurityProtocol dePro = new BaseSecurityProtocol();
            dePro.setSysId(sysId);
            dePro.setBusinessCode(businessCode);
            dePro.setCipherText(recharge.getData());
            String decryptData = encryptOpenService.decrypt(dePro, KeyType.PrivateKey).getPlainTextJson();

            log.info("荣数发券下单解密参数:{}", JSONUtil.toJsonStr(decryptData));
            //2.验签
            PSCSecuritySignBO data = new PSCSecuritySignBO();
            data.setSysId(sysId);
            data.setBusinessCode(businessCode);
            data.setData(decryptData);
            data.setSign(recharge.getSign());
            if (!encryptOpenService.verifySign(data, SignAlgorithm.MD5withRSA)) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_SIGN_FAIL);
            }
            //log.info("荣数发券controller耗时1:{}",System.currentTimeMillis()-time);
            //3.校参
            rechargeBO = JSONUtil.toBean(decryptData, RsRechargeBO.class);
            if (StrUtil.isNotEmpty(rechargeBO.getVoucherTag())) {
                rechargeBO.setVoucherTag(rechargeBO.getVoucherTag().trim());
            }


            rechargeBO.setRequestBody(decryptData);
            rechargeBO.setIpAddress(IpAddrUtil.getRealIp(request));
            rechargeBO.setBusinessCode(businessCode);
            Set<ConstraintViolation<RsRechargeBO>> violationSet = validator.validate(rechargeBO);
            StringBuilder errorSb = new StringBuilder();
            for (ConstraintViolation<RsRechargeBO> model : violationSet) {
                errorSb.append("[").append(model.getPropertyPath()).append("]").append(model.getMessage()).append(";");
            }
            if (StringUtils.hasText(errorSb.toString())) {
                log.error("recharge validate failed: {}", errorSb);
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_PARAMS_FAIL);
            }

            //查询下游商户信息
            ChannelProxyInfoDTO proxyInfoDTO = proxyInfoService.getInfoByCodeCache(rechargeBO.getBusinessCode());
            if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_INFO_IS_NOT_EXIST);
            }
            //白名单校验
            String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
            if (null == ips || !IpWhite.isIpInWhitelist(rechargeBO.getIpAddress(),ips)) {
                throw new RsRechargeException(RsRechargeStatusCode.IP_WHILE_LIST_ERROR);
            }
            //验证代理商商品
            ChannelProxyItemInfoDTO proxyItemInfoDTO = proxyItemInfoService.getByProductId(proxyInfoDTO.getId(),rechargeBO.getVoucherTag());
            if (null == proxyItemInfoDTO || proxyItemInfoDTO.getProductType() == null) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_PRODUCT_NOT_EXIST);
            }
            rechargeBO.setProductType(proxyItemInfoDTO.getProductType());
            rechargeBO.setSpType(proxyItemInfoDTO.getSpType());
            //log.info("荣数发券controller耗时3:{}",System.currentTimeMillis()-time);
            //4.下单
            result = rsRechargeService.rsRecharge(rechargeBO,proxyInfoDTO,proxyItemInfoDTO);
        } catch (RsRechargeException e) {
            log.error("荣数下单异常:{}", e.getMessage());
            result.setCode(e.getStatus().getCode().toString());
            result.setStatus("FAIL");
            result.setMessage(e.getMessage());
            abnormalRechargeRecordService.log(rechargeBO, e.getStatus(), e.getExtraMessage());
        } catch (ChannelProxyInfoException | ChannelSupplierException e) {
            log.error("荣数下单异常:{}", e.getMessage());
            result.setCode(e.getStatus().getCode().toString());
            result.setStatus("FAIL");
            result.setMessage(e.getMessage());
            abnormalRechargeRecordService.log(rechargeBO, e.getStatus(), e.getMessage());
        }
        long workTime = System.currentTimeMillis() - time;
        if (workTime > 1000) {
            log.error("荣数发券请求耗时异常:{}",workTime);
        }
        log.info("荣数发券下单总耗时：{},返回结果:{}",workTime, JSONUtil.toJsonStr(result));
        return result;
    }


    @PostMapping("/rsDirectCharge")
    @Permission(action = Action.Skip)
    public RsDirectChargeResultBO rsDirectCharge(@Valid @RequestBody RsrsDirectChargeRequest recharge, HttpServletRequest request) {
        long time = System.currentTimeMillis();
        log.info("荣数直充下单参数:{}", JSONUtil.toJsonStr(recharge));
        String businessCode = recharge.getVendorNo();
        RsDirectChargeBO rechargeBO = null;
        RsDirectChargeResultBO result = new RsDirectChargeResultBO();
        try {
            //1.解密
            BaseSecurityProtocol dePro = new BaseSecurityProtocol();
            dePro.setSysId(sysId);
            dePro.setBusinessCode(businessCode);
            dePro.setCipherText(recharge.getData());
            String decryptData = encryptOpenService.decrypt(dePro, KeyType.PrivateKey).getPlainTextJson();
            String requestBody = JSON.toJSONString(decryptData);
            log.info("荣数直充下单解密参数:{}", requestBody);
            //2.验签
            PSCSecuritySignBO data = new PSCSecuritySignBO();
            data.setSysId(sysId);
            data.setBusinessCode(businessCode);
            data.setData(decryptData);
            data.setSign(recharge.getSign());
            if (!encryptOpenService.verifySign(data, SignAlgorithm.MD5withRSA)) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_SIGN_FAIL);
            }
            log.info("荣数直充controller耗时1:{}", System.currentTimeMillis() - time);
            //3.校参
            rechargeBO = JSON.parse(decryptData, RsDirectChargeBO.class);
            rechargeBO.setIpAddress(IpAddrUtil.getRealIp(request));
            rechargeBO.setBusinessCode(businessCode);
            rechargeBO.setRequestBody(decryptData);

            //查询下游商户信息
            ChannelProxyInfoDTO proxyInfoDTO = proxyInfoService.getInfoByCodeCache(rechargeBO.getBusinessCode());
            if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_INFO_IS_NOT_EXIST,"商户编码：" + rechargeBO.getBusinessCode());
            }
            //验证代理商商品
            ChannelProxyItemInfoDTO proxyItemInfoDTO = proxyItemInfoService.getByProductId(proxyInfoDTO.getId(), rechargeBO.getRechargeTag());
            if (null == proxyItemInfoDTO) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_PRODUCT_NOT_EXIST,
                        StrUtil.format("商户ID：{}，商户产品编码：{}", proxyInfoDTO.getId(), rechargeBO.getRechargeTag()));
            }

            rechargeBO.setProductType(proxyItemInfoDTO.getProductType());

//            if (RsDirectChargeAccountTypeEnum.MOBILE.getCode().equals(rechargeBO.getAccountType())) {
//                rechargeBO.setProductType(proxyItemInfoDTO.getProductType());
//            } else if (RsDirectChargeAccountTypeEnum.OIL_CARD.getCode().equals(rechargeBO.getAccountType())) {
//                rechargeBO.setProductType(proxyItemInfoDTO.getProductType());
//            }
            Set<ConstraintViolation<RsDirectChargeBO>> violationSet = validator.validate(rechargeBO);
            StringBuilder errorSb = new StringBuilder();
            for (ConstraintViolation<RsDirectChargeBO> model : violationSet) {
                errorSb.append("[").append(model.getPropertyPath()).append("]").append(model.getMessage()).append(";");
            }
            if (StringUtils.hasText(errorSb.toString())) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_DICT_PARAMS_FAIL, errorSb.toString());
            }


            //荣数订单的特殊校验
            rsSpecifyVerify(rechargeBO);


            //白名单校验
            String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
            if (null == ips || !IpWhite.isIpInWhitelist(rechargeBO.getIpAddress(), ips)) {
                throw new RsRechargeException(RsRechargeStatusCode.IP_WHILE_LIST_ERROR,"商户请求IP：" + rechargeBO.getIpAddress());
            }

            //当前仅支持话费充值与油卡充值
            boolean contains = Arrays.asList(RsDirectChargeAccountTypeEnum.MOBILE.getCode(), RsDirectChargeAccountTypeEnum.OIL_CARD.getCode()).contains(rechargeBO.getAccountType());
            if (!contains) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_ACCOUNT_TYPE_NOT_SUPPORT,"不支持的账号类型：" + rechargeBO.getAccountType());
            }

            // 下单日志
            RequestLogDTO logDTO = new RequestLogDTO();
            logDTO.setLogType(RCRequestLogType.PROXY_RECHARGE_LOG);
            logDTO.setPushTimeBegin(new Date());
            logDTO.setResponseBody(decryptData);

            result = rsRechargeService.rsDirectRecharge(rechargeBO, proxyInfoDTO);

            proxyRechargeLog(logDTO, Long.valueOf(result.getData().getVendorOrderNo()));

        } catch (RsRechargeException e) {
            log.error("荣数下单异常:{}", e.getMessage());
            result.setCode(e.getStatus().getCode().toString());
            result.setStatus("FAIL");
            result.setMessage(e.getMessage());
            abnormalRechargeRecordService.log(rechargeBO, e.getStatus(), e.getExtraMessage());
        }
        long workTime = System.currentTimeMillis() - time;
        if (workTime > 1000) {
            log.error("荣数直充请求耗时异常:{}", workTime);
        }
        log.info("荣数直充下单总耗时：{},返回结果:{}", workTime, JSONUtil.toJsonStr(result));
        return result;
    }

    private void proxyRechargeLog(RequestLogDTO logDTO,Long orderId) {
        logDTO.setId(IdUtil.getId());
        logDTO.setOrderId(orderId);
        Date pushTimeEnd = new Date();
        Long pushTimeTotal = pushTimeEnd.getTime() - logDTO.getPushTimeBegin().getTime();
        logDTO.setPushTimeEnd(pushTimeEnd);
        logDTO.setPushTimeTotal(pushTimeTotal);
        // 商户下单日志入库消息
        kafkaTemplate.send(RCAppConst.KAFKA_TOPIC_REQUEST_LOG_SAVE_QUEUE, JSON.toJSONString(logDTO));
    }


    @PostMapping("/rsCancelOrder")
    @Permission(action = Action.Skip)
    public RsCancelOrderResultBO rsCancelOrder(@Valid @RequestBody RsrsCancelOrderRequest recharge, HttpServletRequest request) {
        long time = System.currentTimeMillis();
        log.info("荣数退货参数:{}", JSONUtil.toJsonStr(recharge));
        String businessCode = recharge.getVendorNo();
        RsCancelOrderResultBO result = new RsCancelOrderResultBO();
        try {
            //1.解密
            BaseSecurityProtocol dePro = new BaseSecurityProtocol();
            dePro.setSysId(sysId);
            dePro.setBusinessCode(businessCode);
            dePro.setCipherText(recharge.getData());
            String decryptData = encryptOpenService.decrypt(dePro, KeyType.PrivateKey).getPlainTextJson();
            String requestBody = JSON.toJSONString(decryptData);
            log.info("荣数退货解密参数:{}", requestBody);
            //2.验签
            PSCSecuritySignBO data = new PSCSecuritySignBO();
            data.setSysId(sysId);
            data.setBusinessCode(businessCode);
            data.setData(decryptData);
            data.setSign(recharge.getSign());
            if (!encryptOpenService.verifySign(data, SignAlgorithm.MD5withRSA)) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_SIGN_FAIL);
            }
            log.info("荣数退货controller耗时1:{}",System.currentTimeMillis()-time);
            //3.校参
            RsCancelOrderBO rechargeBO = JSON.parse(decryptData, RsCancelOrderBO.class);
            rechargeBO.setIpAddress(IpAddrUtil.getRealIp(request));
            rechargeBO.setBusinessCode(businessCode);
            rechargeBO.setRequestBody(decryptData);
            Set<ConstraintViolation<RsCancelOrderBO>> violationSet = validator.validate(rechargeBO);
            StringBuilder errorSb = new StringBuilder();
            for (ConstraintViolation<RsCancelOrderBO> model : violationSet) {
                errorSb.append("[").append(model.getPropertyPath() + model.getMessage()).append("]");
            }
            if (StringUtils.hasText(errorSb.toString())) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_DICT_PARAMS_FAIL);
            }

            //查询下游商户信息
            ChannelProxyInfoDTO proxyInfoDTO = proxyInfoService.getInfoByCodeCache(rechargeBO.getBusinessCode());
            if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_INFO_IS_NOT_EXIST);
            }

            //白名单校验
            String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
            if (null == ips || !IpWhite.isIpInWhitelist(rechargeBO.getIpAddress(),ips)) {
                throw new RsRechargeException(RsRechargeStatusCode.IP_WHILE_LIST_ERROR);
            }
            //验证代理商商品
            ChannelProxyItemInfoDTO proxyItemInfoDTO = proxyItemInfoService.getByProductId(proxyInfoDTO.getId(),rechargeBO.getVoucherTag());
            if (null == proxyItemInfoDTO || proxyItemInfoDTO.getProductType() == null) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_PRODUCT_NOT_EXIST);
            }
            //4.下单
            result = rsRechargeService.rsCancelOrder(rechargeBO,proxyInfoDTO,proxyItemInfoDTO);
        } catch (RsRechargeException e) {
            log.error("荣数退货异常:{}", e.getMessage());
            result.setCode(e.getStatus().getCode().toString());
            result.setStatus("FAIL");
            result.setMessage(e.getMessage());
        }

        return result;
    }

    /**
     * 荣数查询卡券状态
     * @param recharge
     * @param request
     * @return
     */
    @PostMapping("/rsVoucherQuery")
    @Permission(action = Action.Skip)
    public RsVoucherStatusResultBO rsQuery(@Valid @RequestBody RsrsVoucherStatusRequest recharge, HttpServletRequest request) {
        long time = System.currentTimeMillis();
        log.info("荣数券码详情查询接口参数:{}", JSONUtil.toJsonStr(recharge));
        String businessCode = recharge.getVendorNo();
        RsVoucherStatusResultBO result = new RsVoucherStatusResultBO();
        try {
            //1.解密
            BaseSecurityProtocol dePro = new BaseSecurityProtocol();
            dePro.setSysId(sysId);
            dePro.setBusinessCode(businessCode);
            dePro.setCipherText(recharge.getData());
            String decryptData = encryptOpenService.decrypt(dePro, KeyType.PrivateKey).getPlainTextJson();
            String requestBody = JSON.toJSONString(decryptData);
            log.info("荣数券码详情查询接口解密参数:{}", requestBody);
            //2.验签
            PSCSecuritySignBO data = new PSCSecuritySignBO();
            data.setSysId(sysId);
            data.setBusinessCode(businessCode);
            data.setData(decryptData);
            data.setSign(recharge.getSign());
            if (!encryptOpenService.verifySign(data, SignAlgorithm.MD5withRSA)) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_SIGN_FAIL);
            }
            log.info("荣数券码详情查询接口controller耗时1:{}",System.currentTimeMillis()-time);
            //3.校参
            RsVoucherQueryBO rechargeBO = JSON.parse(decryptData, RsVoucherQueryBO.class);

            log.info("荣数查询卡券状态rechargeBO: {}",JSON.toJSONString(rechargeBO));

            Set<ConstraintViolation<RsVoucherQueryBO>> violationSet = validator.validate(rechargeBO);
            StringBuilder errorSb = new StringBuilder();
            for (ConstraintViolation<RsVoucherQueryBO> model : violationSet) {
                errorSb.append("[").append(model.getPropertyPath() + model.getMessage()).append("]");
            }
            if (StringUtils.hasText(errorSb.toString())) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_DICT_PARAMS_FAIL);
            }

            //查询下游商户信息
            ChannelProxyInfoDTO proxyInfoDTO = proxyInfoService.getInfoByCodeCache(businessCode);
            if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_INFO_IS_NOT_EXIST);
            }

            //白名单校验
            String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
            if (null == ips || !IpWhite.isIpInWhitelist(IpAddrUtil.getRealIp(request),ips)) {
                throw new RsRechargeException(RsRechargeStatusCode.IP_WHILE_LIST_ERROR);
            }
            //验证是卡券订单还是卡密订单
            RsVoucherStatusResultDataValue voucherStatusResultBO=rsRechargeService.rsVoucherQuery(proxyInfoDTO,rechargeBO);

            result.setCode("00000");
            result.setMessage("");
            result.setStatus("SUCCESS");
            result.setData(voucherStatusResultBO);
            log.info("荣数查询卡券状态rechargeBO: {},result: {}",JSON.toJSONString(rechargeBO),JSON.toJSONString(result));
            return result;
        } catch (RsRechargeException e) {
            log.error("荣数券码详情查询异常:{}", e.getMessage());
            result.setCode("90000");
            result.setStatus("FAIL");
            result.setMessage(e.getMessage());
        }catch (Exception e) {
            log.error("荣数券码详情查询未知异常:{}", e.getMessage(),e);
            result.setCode("90000");
            result.setStatus("FAIL");
            result.setMessage("");
        }

        log.info("荣数查询卡券状态结果: {}",JSON.toJSONString(result));
        return result;
    }

    /**
     * 荣数补单模板下载
     */
    @RequestMapping("/rsPushTemplate")
    @Permission(value = "api:rc:recharge:rsPushTemplate")
    public void rsPushTemplate(HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            String fileName = URLEncoder.encode(String.format("荣数补单模板"), StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", String.format("attachment;filename=%s.xlsx", fileName));
            List<RsRePushOrderImportDTO> data = new ArrayList<>();
            RsRePushOrderImportDTO dto = new RsRePushOrderImportDTO();
            dto.setBusinessCode("proxy_ylshshrs");
            dto.setSipOrderNo("示例: 202403281731U0892331(荣数单号)");
            dto.setVoucherTag("示例: shrs-js-30-xyk-ACT531CCV02088197");
            dto.setAccountNo("手机号,邮箱,openId等");
            dto.setAccountType("1-手机号、2-微信、openid、3-邮箱、4-支付宝openid");
            dto.setAccountInfo("微信公众号appid等");
            data.add(dto);
            EasyExcel.write(response.getOutputStream(), RsRePushOrderImportDTO.class).autoCloseStream(Boolean.FALSE).sheet(0).doWrite(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 荣数补单
     * 适用场景: 荣数推单到充值中心失败，需要我方手动补单
     * 功能描述: 通过导入补单文件，跳过加解密，直接生成订单相关数据，并记录日志，补了哪些单，谁补的
     * @param file
     * @return
     */
    @PostMapping("/rsOrderRePush")
    @Permission(value = "api:rc:recharge:rsOrderRePush")
    public void rsOrderRePush(MultipartFile file, HttpServletRequest request, HttpServletResponse response, @Token TokenInfo tokenInfo) {
        if (file == null || file.isEmpty()) {
            throw new RsRechargeException(RsRechargeStatusCode.RS_RE_PUSH_FILE_IS_NULL);
        }
        List<RsRePushOrderImportDTO> dtoList = new ArrayList<>();
        try (InputStream inputStream = file.getInputStream()){
            dtoList = EasyExcelFactory.read(inputStream).head(RsRePushOrderImportDTO.class).sheet().doReadSync();
        } catch (IOException e) {
            log.error("读取荣数补单文件异常:{}, {}", e.getMessage(), e);
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setFailed();
            baseResponse.setMsg("读取荣数补单文件异常");
            ResponseUtil.responseJson(response, baseResponse);
        }

        List<RsRePushOrderImportResultDTO> pushResult = new ArrayList<>();

        for (RsRePushOrderImportDTO importDTO : dtoList) {
            RsRePushOrderImportResultDTO importResult = BeanUtil.toBean(importDTO, RsRePushOrderImportResultDTO.class);
            RsRechargeBO rechargeBO = BeanUtil.toBean(importDTO, RsRechargeBO.class);
            rechargeBO.setIpAddress(IpAddrUtil.getRealIp(request));
            rechargeBO.setRequestBody(JSONUtil.toJsonStr(rechargeBO));
            List<String> checkList = new ArrayList<>();

            String key = RCAppConst.RS_RE_PUSH_ORDER_BIZ + rechargeBO.getSipOrderNo();
            Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, rechargeBO.getSipOrderNo(), 1, TimeUnit.MINUTES);
            if (Boolean.FALSE.equals(lock)) {
                checkList.add("该订单号正在补发,无法抢占锁,请检查荣数单号是否有问题或1分钟之后再重试");
            }

            //校验参数
            Set<ConstraintViolation<RsRechargeBO>> violationSet = validator.validate(rechargeBO);
            StringBuilder errorSb = new StringBuilder();
            for (ConstraintViolation<RsRechargeBO> model : violationSet) {
                errorSb.append("[").append(model.getPropertyPath() + model.getMessage()).append("]");
            }
            if (errorSb.length() > 0) {
                checkList.add("参数不全,请检查: " + errorSb);
            }

            //查询下游商户信息
            ChannelProxyInfoDTO proxyInfoDTO = proxyInfoService.getAccountInfoByCode(rechargeBO.getBusinessCode());
            if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
                checkList.add("商户信息不存在");
            }

            //白名单校验
            if (proxyInfoDTO != null) {
                String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
                if (null == ips || !IpWhite.isIpInWhitelist(rechargeBO.getIpAddress(),ips)) {
                    checkList.add("IP:" + rechargeBO.getIpAddress() + "不在白名单内");
                }
            }

            //验证代理商商品
            ChannelProxyItemInfoDTO proxyItemInfoDTO = null;
            if (proxyInfoDTO != null) {
                proxyItemInfoDTO = proxyItemInfoService.getByProductId(proxyInfoDTO.getId(), rechargeBO.getVoucherTag());
                if (null == proxyItemInfoDTO) {
                    checkList.add("代理商商品不存在");
                }
            }

            //校验荣数单号是否存在
            if (proxyInfoDTO != null) {
                int bizIdExist = virtualCouponOrderService.countByProxyCustomBizId(proxyInfoDTO.getId(), rechargeBO.getSipOrderNo());
                if (bizIdExist > 0) {
                    checkList.add("荣数单号已经存在");
                }
            }

            //校验不通过,执行下一条补发
            if (checkList.size() > 0) {
                importResult.setResult("补单失败: " + checkList.stream().collect(Collectors.joining(";")));
                pushResult.add(importResult);
                continue;
            }

            try {
                log.info("荣数补单请求:{}", JSONUtil.toJsonStr(rechargeBO));
                RsRechargeResultBO resultBO = rsRechargeService.rsRecharge(rechargeBO, proxyInfoDTO, proxyItemInfoDTO);
                log.info("荣数补单结果:{}", JSONUtil.toJsonStr(resultBO));

                if (resultBO != null && "00001".equals(resultBO.getCode())) {
                    log.info("补单成功:{}", rechargeBO.getSipOrderNo());
                    importResult.setResult("补单成功");
                    //补单成功则保存日志
                    RepushOrderLogDTO logDTO = new RepushOrderLogDTO();
                    logDTO.setProxyId(proxyInfoDTO.getId());
                    logDTO.setProxyCode(proxyInfoDTO.getProxyCode());
                    logDTO.setPushUser(tokenInfo.getUserDetail().getAgentId());
                    logDTO.setProxyCustomBizId(importDTO.getSipOrderNo());
                    logDTO.setPushOrderContent(JSONUtil.toJsonStr(importDTO));
                    repushOrderLogService.savePushLog(logDTO);
                }
            } catch (Exception e) {
                log.info("荣数补单失败:{},{}", e.getMessage(), e);
                importResult.setResult("补单失败:" + e.getMessage());
            }
            pushResult.add(importResult);
        }

        try {
            String fileName = URLEncoder.encode(String.format("荣数补单结果"), StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader("Content-disposition", String.format("attachment;filename=%s.xlsx", fileName));
            EasyExcel.write(response.getOutputStream(), RsRePushOrderImportResultDTO.class).autoCloseStream(Boolean.FALSE).sheet("导入结果").doWrite(pushResult);
        } catch (Exception e) {
            log.error("读取荣数补单文件异常:{}, {}", e.getMessage(), e);
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setFailed();
            baseResponse.setMsg("读取荣数补单文件异常");
            ResponseUtil.responseJson(response, baseResponse);
        }

    }

    private void rsSpecifyVerify(RsDirectChargeBO rechargeBO) {
        if (Integer.valueOf(1).equals(rechargeBO.getAccountType())) {
            String accountNo = rechargeBO.getAccountNo();
            boolean match = ReUtil.isMatch(RCRegExConst.PHONE_LOOSE, accountNo);
            if (!match) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_ACCOUNT_FAIL,"手机号码格式不正确："+accountNo);
            }
        } else if (Integer.valueOf(6).equals(rechargeBO.getAccountType())) {
            String accountNo = rechargeBO.getAccountNo();
            String oilCardMatchRuleConfig = getOilCardMatchRuleConfig();
            String regex = StrUtil.format("({}|{}|{})", RCRegExConst.SINOPEC,RCRegExConst.PETRO_CHINA,RCRegExConst.PETRO_CHINA_70);
            if (StrUtil.isNotBlank(oilCardMatchRuleConfig)){
                if (oilCardMatchRuleConfig.contains(",")){
                    oilCardMatchRuleConfig = oilCardMatchRuleConfig.replaceAll(",", "|");
                }
                StringBuilder builder = new StringBuilder();
                builder.append("(").append(oilCardMatchRuleConfig).append(")");
                regex = builder.toString();
            }
            log.info("油卡规则={}",regex);
            boolean match = ReUtil.isMatch(regex, accountNo);
            if (!match) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_ACCOUNT_FAIL,"油卡卡号格式不正确："+accountNo);
            }
        }
    }

    /**
     * 获取油卡卡号校验规则
     * @return
     */
    private String getOilCardMatchRuleConfig(){
        StringBuilder builder = new StringBuilder();
        PSCSystemConfigBO zshConfig = pscSystemConfigOpenService.getByCode(sysId, RCAppConst.SAAS_RC_OIL_CARD_ZSH_MATCH_RULE);
        PSCSystemConfigBO zsyConfig = pscSystemConfigOpenService.getByCode(sysId, RCAppConst.SAAS_RC_OIL_CARD_ZSY_MATCH_RULE);
        if (zshConfig != null && StrUtil.isNotEmpty(zshConfig.getConfigValue())){
            builder.append(zshConfig.getConfigValue());
        }
        if (zsyConfig != null && StrUtil.isNotEmpty(zsyConfig.getConfigValue())){
            if (StrUtil.isNotEmpty(builder.toString())){
                builder.append(",");
            }
            builder.append(zsyConfig.getConfigValue());
        }
        return builder.toString();
    }


    @PostMapping("/rsAliActivityInfo")
    @Permission(action = Action.Skip)
    public RsGetAliActivityInfoResultBO rsAliActivityInfo(@Valid @RequestBody RsRechargeRequest recharge, HttpServletRequest request) {
        long time = System.currentTimeMillis();
        //log.info("荣数请求支付宝活动接口信息:{}", JSONUtil.toJsonStr(recharge));
        String businessCode = recharge.getVendorNo();
        RsGetAliActivityInfoResultBO result = new RsGetAliActivityInfoResultBO();
        RsGetAliActivityInfoBO rechargeBO = null;
        try {
            //1.解密
            String decryptData = decryptDataMethod(businessCode, recharge.getData());

            //log.info("荣数查询支付宝活动解密参数:{}", JSONUtil.toJsonStr(decryptData));
            //2.验签
            checkSignMethod(recharge, businessCode, decryptData);

            //log.info("荣数查询支付宝活动解密参数:{}",System.currentTimeMillis()-time);

            //3.校参
            rechargeBO = JSON.parse(decryptData, RsGetAliActivityInfoBO.class);

            Set<ConstraintViolation<RsGetAliActivityInfoBO>> violationSet = validator.validate(rechargeBO);
            StringBuilder errorSb = new StringBuilder();
            for (ConstraintViolation<RsGetAliActivityInfoBO> model : violationSet) {
                errorSb.append("[").append(model.getPropertyPath()).append("]").append(model.getMessage()).append(";");
            }
            if (StringUtils.hasText(errorSb.toString())) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_PARAMS_FAIL);
            }


            result = rsRechargeService.rsGetAliActivityInfo(businessCode,rechargeBO);



        } catch (RsRechargeException e) {
            log.error("荣数下单异常:{}", e.getMessage());
            result.setCode(e.getStatus().getCode().toString());
            result.setStatus("FAIL");
            result.setMessage(e.getMessage());
        }
        long workTime = System.currentTimeMillis() - time;
        if (workTime > 1000) {
            log.error("荣数查询支付宝活动耗时异常:{}",workTime);
        }
        log.info("荣数查询支付宝活动总耗时：{},返回结果:{}",workTime, JSONUtil.toJsonStr(result));
        return result;
    }


    @PostMapping("/rsWxActivityInfo")
    @Permission(action = Action.Skip)
    public RsGetWxActivityInfoResultBO rsWxActivityInfo(@Valid @RequestBody RsRechargeRequest recharge, HttpServletRequest request) {
        long time = System.currentTimeMillis();
        //log.info("荣数请求微信活动接口信息:{}", JSONUtil.toJsonStr(recharge));
        String businessCode = recharge.getVendorNo();
        RsGetWxActivityInfoResultBO result = new RsGetWxActivityInfoResultBO();
        RsGetWxActivityInfoBO rechargeBO = null;
        try {
            //1.解密
            String decryptData = decryptDataMethod(businessCode, recharge.getData());

            //log.info("荣数查询微信活动解密参数:{}", JSONUtil.toJsonStr(decryptData));
            //2.验签
            checkSignMethod(recharge, businessCode, decryptData);

            //log.info("荣数查询微信活动解密参数:{}", System.currentTimeMillis() - time);

            //3.校参
            rechargeBO = JSON.parse(decryptData, RsGetWxActivityInfoBO.class);

            Set<ConstraintViolation<RsGetWxActivityInfoBO>> violationSet = validator.validate(rechargeBO);
            StringBuilder errorSb = new StringBuilder();
            for (ConstraintViolation<RsGetWxActivityInfoBO> model : violationSet) {
                errorSb.append("[").append(model.getPropertyPath()).append("]").append(model.getMessage()).append(";");
            }
            if (StringUtils.hasText(errorSb.toString())) {
                throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_PARAMS_FAIL);
            }


            result = rsRechargeService.rsGetWxActivityInfo(businessCode, rechargeBO);


        } catch (RsRechargeException e) {
            log.error("荣数查询微信批次异常:{}", e.getMessage());
            result.setCode("90000");
            result.setStatus("FAIL");
            result.setMessage(e.getMessage());
        } catch (Exception e) {
            result.setCode("90000");
            result.setStatus("FAIL");
        }
        long workTime = System.currentTimeMillis() - time;
        if (workTime > 1000) {
            log.error("荣数查询支付宝活动耗时异常:{}",workTime);
        }
        log.info("荣数查询微信活动总耗时：{},返回结果:{}",workTime, JSONUtil.toJsonStr(result));
        return result;
    }


    private String decryptDataMethod(String businessCode,String cipherText) {
        BaseSecurityProtocol dePro = new BaseSecurityProtocol();
        dePro.setSysId(sysId);
        dePro.setBusinessCode(businessCode);
        dePro.setCipherText(cipherText);
        return encryptOpenService.decrypt(dePro, KeyType.PrivateKey).getPlainTextJson();
    }

    private void checkSignMethod(RsRechargeRequest recharge, String businessCode, String decryptData) {
        PSCSecuritySignBO data = new PSCSecuritySignBO();
        data.setSysId(sysId);
        data.setBusinessCode(businessCode);
        data.setData(decryptData);
        data.setSign(recharge.getSign());
        if (!encryptOpenService.verifySign(data, SignAlgorithm.MD5withRSA)) {
            throw new RsRechargeException(RsRechargeStatusCode.RS_RECHARGE_CHECK_SIGN_FAIL);
        }
    }

}
