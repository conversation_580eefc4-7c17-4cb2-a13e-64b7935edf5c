package cn.joysim.cloud.rc.controller.open;


import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.rc.controller.request.ProxySpcProductRequest;
import cn.joysim.cloud.rc.controller.request.ProxySpcRechargeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Profile({"dev", "uat","test"})
@RestController
@RequestMapping("/api/spc/v1")
@Slf4j
public class SpcTestController {

    private static String APP_KEY = "e5usbtdybudn83i";

    private static String AES_KEY = "J14nX4RsuZ0Jc99V";

    private static String AES_IV = "kHLzyaPLtQhpEALQ";

    @PostMapping("/getChargeRequest")
    @Permission(action = Action.Skip)
    public Map getChargeRequest(@RequestBody ProxySpcRechargeRequest request) throws Exception {
        Long timestamp = DateUtil.current();
        String data = dataEncrypt(JSONUtil.toJsonStr(request), AES_KEY, AES_IV);
        String sign = getSign(APP_KEY, data, String.valueOf(timestamp));

        Map<String, String> result = new HashMap<>();
        result.put("appKey", APP_KEY);
        result.put("data", data);
        result.put("sign", sign);
        result.put("timestamp", timestamp.toString());
        result.put("merOrderId", request.getMerOrderId());
        log.info(JSONUtil.toJsonStr(result));
        return result;
    }

    @PostMapping("/getProductRequest")
    @Permission(action = Action.Skip)
    public Map getProductRequest(@RequestBody ProxySpcProductRequest request) throws Exception {
        Long timestamp = DateUtil.current();
        String data = dataEncrypt(JSONUtil.toJsonStr(request), AES_KEY, AES_IV);
        String sign = getSign(APP_KEY, data, String.valueOf(timestamp));
        Map<String, String> result = new HashMap<>();
        result.put("appKey", APP_KEY);
        result.put("data", data);
        result.put("sign", sign);
        result.put("timestamp", timestamp.toString());
        log.info(JSONUtil.toJsonStr(result));
        return result;
    }

    public static String dataEncrypt(String data, String key, String iv) throws Exception {
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] encryptedBytes = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public static String dataDecrypt(String encryptedData, String key, String iv) throws Exception {
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
        SecretKeySpec secretKeySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");

        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

        byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        return new String(decryptedBytes, StandardCharsets.UTF_8);
    }

    public static String getSign(String appKey, String data, String timestamp) {
        String str = "appKey=" + appKey + "&data=" + data + "&timestamp=" + timestamp;
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        byte[] encodedHash = digest.digest(str.getBytes(StandardCharsets.UTF_8));
        StringBuilder hexString = new StringBuilder();
        for (byte b : encodedHash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        String upSign = hexString.toString().toUpperCase();
        return upSign;
    }

}
