package cn.joysim.cloud.rc.controller.open;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Decrypt;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.controller.BaseController;
import cn.joysim.cloud.common.model.dto.BaseSecurityProtocol;
import cn.joysim.cloud.common.model.vo.BaseResponse;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.psc.service.PSCEncryptOpenService;
import cn.joysim.cloud.psc.service.PSCSystemConfigOpenService;
import cn.joysim.cloud.rc.common.exception.RechargeException;
import cn.joysim.cloud.rc.common.exception.status.RechargeStatusCode;
import cn.joysim.cloud.rc.common.model.pojo.enums.RechargeProductType;
import cn.joysim.cloud.rc.controller.request.*;
import cn.joysim.cloud.rc.model.bo.*;
import cn.joysim.cloud.rc.model.dto.SpTypeInfoOpenApiBO;
import cn.joysim.cloud.rc.model.dto.request.ProxyDoRechargeMobilePhoneRequest;
import cn.joysim.cloud.rc.model.dto.request.ProxyDoRechargeOilCardRequest;
import cn.joysim.cloud.rc.model.dto.request.ProxyDoRechargeXingCanRequest;
import cn.joysim.cloud.rc.model.dto.request.SpTypeInfoQueryRequest;
import cn.joysim.cloud.rc.service.RechargeService;
import cn.joysim.cloud.rc.utils.ValidateUtil;
import cn.joysim.cloud.rc.validate.RechargeCouponDynamicVerifyFactory;
import cn.joysim.cloud.rc.validate.RechargeCouponDynamicVerifyItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import javax.validation.Validator;
import java.util.List;

/**
 * 充值管理接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/rc/recharge")
@Slf4j
public class RCRechargeController extends BaseController {


    @Resource
    private RechargeService rechargeService;

    @Resource
    private Validator validator;

    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCSystemConfigOpenService configService;

    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCEncryptOpenService encryptOpenService;

    @Value("${common.application.id}")
    private Long sysId;


    @Resource
    private RechargeCouponDynamicVerifyFactory rechargeCouponDynamicVerifyFactory;

    /**
     * 下单充值
     *
     * @param decryptData
     * @return
     */
    @PostMapping("/doRecharge")
    @Permission(action = Action.Skip)
    public BaseResponse<DoRechargeResultBO> doRecharge(@Decrypt BaseSecurityProtocol decryptData,
                                                       HttpServletRequest request) {

        Object planTextObj = decryptData.getPlainTextObj();
        ProxyDoRechargeCommonRequest requestData = BeanUtil.toBean(planTextObj, ProxyDoRechargeCommonRequest.class);
        log.info("do recharge info = {}", requestData);

        requestData.setBusinessCode(decryptData.getBusinessCode());
        StringBuilder errorSb = new StringBuilder();
        // 公共参数校验
        ValidateUtil.commonValidate(requestData, errorSb);
        RechargeProductType productType = RechargeProductType.fromCode(requestData.getProductType());
        DoRechargeBO doRechargeDTO = new DoRechargeBO();
        doRechargeDTO.setIpAddress(IpAddrUtil.getRealIp(request));
        if (RechargeProductType.RECHARGE_MOBILE_PHONE == productType) {
            // 话费充值 业务参数校验
            ProxyDoRechargeMobilePhoneRequest mobilePhoneRequestData = BeanUtil.toBean(requestData, ProxyDoRechargeMobilePhoneRequest.class);
            ValidateUtil.commonValidate(mobilePhoneRequestData, errorSb);
            if (StringUtils.hasText(errorSb.toString())) {
                BaseResponse response = new BaseResponse();
                response.setFailed();
                response.setData(errorSb.toString());
                return response;
            }
            BeanUtil.copyProperties(mobilePhoneRequestData, doRechargeDTO);
        } else if (RechargeProductType.RECHARGE_OIL_CARD == productType) {
            // 油卡充值 业务参数校验
            ProxyDoRechargeOilCardRequest oilCardRequestData = BeanUtil.toBean(requestData, ProxyDoRechargeOilCardRequest.class);
            ValidateUtil.commonValidate(oilCardRequestData, errorSb);
            if (StringUtils.hasText(errorSb.toString())) {
                BaseResponse response = new BaseResponse();
                response.setFailed();
                response.setMsg(errorSb.toString());
                return response;
            }
            BeanUtil.copyProperties(oilCardRequestData, doRechargeDTO);
        } else if (RechargeProductType.RECHARGE_CARD_CIPHER == productType) {
            // 卡密充值 业务参数校验
            ProxyDoRechargeCardCipherRequest cardCipherRequestData = BeanUtil.toBean(requestData, ProxyDoRechargeCardCipherRequest.class);
            ValidateUtil.commonValidate(cardCipherRequestData, errorSb);
            if (StringUtils.hasText(errorSb.toString())) {
                BaseResponse response = new BaseResponse();
                response.setFailed();
                response.setMsg(errorSb.toString());
                return response;
            }
            BeanUtil.copyProperties(cardCipherRequestData, doRechargeDTO);
        } else if (RechargeProductType.RECHARGE_VIRTUAL_COUPON == productType){
            //虚拟卡券 业务参数校验
            ProxyDoRechargeVirtualCouponRequest virtualCouponRequestData = BeanUtil.toBean(requestData, ProxyDoRechargeVirtualCouponRequest.class);
            ValidateUtil.commonValidate(virtualCouponRequestData,errorSb);
            List<RechargeCouponDynamicVerifyItem> verifyItems = rechargeCouponDynamicVerifyFactory.getByBusinessCode(requestData.getBusinessCode());
            couponDynamicValidate(virtualCouponRequestData,verifyItems,errorSb);
            if (StringUtils.hasText(errorSb.toString())){
                BaseResponse response = new BaseResponse();
                response.setFailed();
                response.setMsg(errorSb.toString());
                return response;
            }
            BeanUtil.copyProperties(virtualCouponRequestData,doRechargeDTO);
        } else if (RechargeProductType.RECHARGE_NH_INTEGRAL_EXCHANGE == productType){
            ProxyDoRechargeNhIntegralExchangeRequest nhIntegralExchangeRequestData = BeanUtil.toBean(requestData, ProxyDoRechargeNhIntegralExchangeRequest.class);
            ValidateUtil.commonValidate(nhIntegralExchangeRequestData,errorSb);
            if(StringUtils.hasText(errorSb.toString())){
                BaseResponse response = new BaseResponse();
                response.setFailed();
                response.setMsg(errorSb.toString());
                return response;
            }
            BeanUtil.copyProperties(nhIntegralExchangeRequestData,doRechargeDTO);
        } else if(RechargeProductType.RECHARGE_XING_CAN == productType){
            ProxyDoRechargeXingCanRequest xingCanRequestData=BeanUtil.toBean(requestData,ProxyDoRechargeXingCanRequest.class);
            ValidateUtil.commonValidate(xingCanRequestData,errorSb);
            if(StringUtils.hasText(errorSb.toString())){
                BaseResponse response = new BaseResponse();
                response.setFailed();
                response.setMsg(errorSb.toString());
                return response;
            }
            BeanUtil.copyProperties(xingCanRequestData,doRechargeDTO);
        }else {
            throw new RechargeException(RechargeStatusCode.PRODUCT_TYPE_ERROR);
        }
        doRechargeDTO.setProductType(productType.getCode());
        DoRechargeResultBO result = rechargeService.doRecharge(doRechargeDTO);
        return successResponse(result);
    }

    private void couponDynamicValidate(ProxyDoRechargeVirtualCouponRequest virtualCouponRequestData, List<RechargeCouponDynamicVerifyItem> verifyItems, StringBuilder errorSb) {
        for (RechargeCouponDynamicVerifyItem verifyItem : verifyItems) {
            try {
                verifyItem.valid(virtualCouponRequestData);
            } catch (ValidationException validationException) {
                errorSb.append("[").append(validationException.getMessage()).append("]");
            }
        }
    }



    /**
     * 查询订单
     *
     * @param decryptData
     * @return
     */
    @PostMapping("/query")
    @Permission(action = Action.Skip)
    public BaseResponse<List<RCRechargeOrderInfoBO>> query(@Decrypt BaseSecurityProtocol decryptData) {

        Object planTextObj = decryptData.getPlainTextObj();
        ProxyRechargeOrderQueryRequest requestData = BeanUtil.toBean(planTextObj, ProxyRechargeOrderQueryRequest.class);

        log.info("parse obj = {}", requestData);

        requestData.setBusinessCode(decryptData.getBusinessCode());

        // 参数校验
        StringBuilder errorSb = new StringBuilder();
        ValidateUtil.commonValidate(requestData, errorSb);
        if (StringUtils.hasText(errorSb.toString())) {
            BaseResponse response = new BaseResponse();
            response.setFailed();
            response.setMsg(errorSb.toString());
            return response;
        }
        RechargeProductType productType = RechargeProductType.fromCode(requestData.getProductType());
        List<RCRechargeOrderInfoBO> orderList = rechargeService
                .query(requestData.getBusinessCode(), requestData.getCustomBizIds(), productType);

        if (null == orderList || orderList.size() == 0) {
            throw new RechargeException(RechargeStatusCode.ORDER_IS_NOT_EXIST);
        }
        log.info("订单号={}，产品类型={}；查询结果={}", requestData.getCustomBizIds(),productType.getText(),JSONUtil.toJsonStr(orderList));
        return successResponse(orderList);
    }

    /**
     * 查询账户信息
     *
     * @param protocol
     * @return
     */
    @PostMapping("/getAccountInfo")
    @Permission(action = Action.Skip)
    public BaseResponse<ProxyAccountInfoBO> getAccountInfo(@Decrypt BaseSecurityProtocol protocol) {

        String data = protocol.getPlainTextJson();

        log.info("getAccountInfo data= {}", data);

        ProxyGetAccountInfoRequest accountInfoRequest = JSON.parse(data, ProxyGetAccountInfoRequest.class);

        // 协定核对
        if (!protocol.getBusinessCode().equals(accountInfoRequest.getBusinessCode())) {
            throw new RechargeException(RechargeStatusCode.PROXY_INFO_CHECK_FAILED);
        }

        ProxyAccountInfoBO accountInfoBO = rechargeService.getAccountInfo(protocol.getBusinessCode());

        return successResponse(accountInfoBO);
    }


    /**
     * 商户查询商户商品信息
     * @param protocol
     * @return
     */
    @PostMapping("/getProxyItemInfo")
    @Permission(action = Action.Skip)
    public BaseResponse<List<ProxyItemInfoBO>> getProxyItemInfo(@Decrypt BaseSecurityProtocol protocol){
        String data = protocol.getPlainTextJson();
        ProxyItemInfoQueryRequest request = JSON.parse(data, ProxyItemInfoQueryRequest.class);
        log.info("获取商户商品信息请求参数 request= {}", request);

        // 协定核对
        if (!protocol.getBusinessCode().equals(request.getBusinessCode())) {
            throw new RechargeException(RechargeStatusCode.PROXY_INFO_CHECK_FAILED);
        }
        List<ProxyItemInfoBO> proxyItemInfoList = rechargeService.getProxyItemInfo(request.getBusinessCode());
        return successResponse(proxyItemInfoList);
    }

    /**
     * 获取SpType信息
     * @param protocol
     * @return
     */
    @PostMapping("/getSpTypeInfo")
    @Permission(action = Action.Skip)
    public BaseResponse<List<SpTypeInfoOpenApiBO>> getSpTypeInfo(@Decrypt BaseSecurityProtocol protocol){
        String data = protocol.getPlainTextJson();
        SpTypeInfoQueryRequest request = JSON.parse(data, SpTypeInfoQueryRequest.class);
        log.info("获取SpType信息请求参数 request= {}", request);

        // 协定核对
        if (!protocol.getBusinessCode().equals(request.getBusinessCode())) {
            throw new RechargeException(RechargeStatusCode.PROXY_INFO_CHECK_FAILED);
        }
        List<SpTypeInfoOpenApiBO> spTypeInfoList = rechargeService.getSpTypeInfo(protocol.getBusinessCode(),request.getSpTypeCode());
        return successResponse(spTypeInfoList);
    }
}
