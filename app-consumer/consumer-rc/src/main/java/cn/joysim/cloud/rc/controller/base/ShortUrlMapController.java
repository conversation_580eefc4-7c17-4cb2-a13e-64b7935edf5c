package cn.joysim.cloud.rc.controller.base;


import cn.hutool.core.util.StrUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.controller.BaseController;
import cn.joysim.cloud.common.model.vo.BaseResponse;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.common.exception.RechargeException;
import cn.joysim.cloud.rc.common.model.pojo.enums.ShortUrlType;
import cn.joysim.cloud.rc.service.ShortUrlMapService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


/**
 * 短链控制器
 *
 * <AUTHOR>
 * @date 2023-07-03
 */
@RestController
@Slf4j
public class ShortUrlMapController extends BaseController {



    @Resource
    private ShortUrlMapService shortUrlMapService;
    /**
     * 短链请求
     *
     * @return
     */
    @SneakyThrows
    @RequestMapping("/a/{shortUrl}")
    @Permission(action = Action.Skip)
    public void shortUrlMapA(HttpServletResponse response, @PathVariable("shortUrl")String shortUrl) {
        log.info("a类短链请求shortUrl: {}",shortUrl);
        String fallBackUrl = shortUrlMapService.getByShortUrl(ShortUrlType.MP,shortUrl);
        response.sendRedirect(fallBackUrl);
    }

    /**
     * 短链请求
     *
     * @return
     */
    @SneakyThrows
    @RequestMapping("/b/{shortUrl}")
    @Permission(action = Action.Skip)
    public void shortUrlMapB(HttpServletResponse response, @PathVariable("shortUrl")String shortUrl) {
        log.info("b类短链请求shortUrl: {}",shortUrl);
        String fallBackUrl = shortUrlMapService.getByShortUrl(ShortUrlType.H5,shortUrl);
        response.sendRedirect(fallBackUrl);
    }

    /**
     * 星灿家政短链请求
     *
     * @return
     */
    @SneakyThrows
    @RequestMapping("/c/{shortUrl}")
    @Permission(action = Action.Skip)
    public void shortUrlMapC(HttpServletResponse response, HttpServletRequest servletRequest, @PathVariable("shortUrl")String shortUrl) {
        log.info("c类短链请求shortUrl: {}",shortUrl);
        String realIp = IpAddrUtil.getRealIp(servletRequest);
        String fallBackUrl = shortUrlMapService.getByShortUrlByXingCan(realIp,shortUrl);
        response.sendRedirect(fallBackUrl);
    }

    /**
     * 车主卡密链接
     *
     * @return
     */
    @SneakyThrows
    @RequestMapping("/d/{shortUrl}")
    @Permission(action = Action.Skip)
    public void shortUrlMapD(HttpServletResponse response, HttpServletRequest servletRequest, @PathVariable("shortUrl")String shortUrl) {
        log.info("d类短链请求shortUrl: {}",shortUrl);
        String realIp = IpAddrUtil.getRealIp(servletRequest);
        String fallBackUrl = shortUrlMapService.getByShortUrl(ShortUrlType.CZFW,shortUrl);
        response.sendRedirect(fallBackUrl);
    }


}

