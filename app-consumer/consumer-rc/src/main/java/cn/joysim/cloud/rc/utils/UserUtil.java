package cn.joysim.cloud.rc.utils;

import cn.joysim.cloud.rc.model.dto.AsyncDownloadAdminUserInfoDTO;
import cn.joysim.cloud.upc.common.model.bo.TokenInfo;

public class UserUtil {

    public static AsyncDownloadAdminUserInfoDTO toAsyncDownloadUser(TokenInfo tokenInfo) {
        AsyncDownloadAdminUserInfoDTO infoDTO = new AsyncDownloadAdminUserInfoDTO();
        infoDTO.setId(tokenInfo.getUserDetail().getId());
        infoDTO.setAgentName(tokenInfo.getUserDetail().getAgentName());
        return infoDTO;
    }
}
