package cn.joysim.cloud.rc.controller.open;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.split.SplitIter;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Decrypt;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.controller.BaseController;
import cn.joysim.cloud.common.model.dto.BaseSecurityProtocol;
import cn.joysim.cloud.common.model.vo.BaseResponse;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.psc.common.model.bo.PSCSystemConfigBO;
import cn.joysim.cloud.psc.service.PSCSystemConfigOpenService;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.common.exception.RechargeException;
import cn.joysim.cloud.rc.controller.request.*;
import cn.joysim.cloud.rc.model.bo.DoQueryWxBatchExistResultBO;
import cn.joysim.cloud.rc.model.bo.DoQueryWxBatchInfoResultBO;
import cn.joysim.cloud.rc.model.dto.activity.wx.ChannelWxActivityDTO;
import cn.joysim.cloud.rc.service.activity.ChannelWxActivityService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

import static cn.joysim.cloud.rc.common.exception.status.RechargeStatusCode.RECHARGE_QUERY_BATCH_NOT_SUPPORT;

@RestController
@RequestMapping("/api/rc/batchInfo")
@Slf4j
public class RCBatchInfoController extends BaseController {


    @Resource
    private ChannelWxActivityService channelWxActivityService;


    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCSystemConfigOpenService pscSystemConfigOpenService;


    @Value("${common.application.id}")
    private Long sysId;
    /**
     *  获取所有在充值中心存在的批次
     *
     * @param decryptData
     * @return
     */
    @PostMapping("/wx")
    @Permission(action = Action.Skip)
    public BaseResponse<DoQueryWxBatchInfoResultBO> allWxBatch(@Decrypt BaseSecurityProtocol decryptData,
                                                       HttpServletRequest request) {

        //检查接口商户是否允许调用当前接口
        checkEnableQuery(decryptData);

        Object planTextObj = decryptData.getPlainTextObj();
        RCWxBatchExistRequest requestData = BeanUtil.toBean(planTextObj, RCWxBatchExistRequest.class);
        log.info("do recharge info = {}", requestData);

        requestData.setBusinessCode(decryptData.getBusinessCode());
        List<String> batchIds=channelWxActivityService.getAllBatchId();

        DoQueryWxBatchInfoResultBO resultBO = new DoQueryWxBatchInfoResultBO();
        resultBO.setBatchIds(batchIds);

        return successResponse(resultBO);
    }

    /**
     * 判断当前批次是否在充值中心中存在
     * @param decryptData
     * @param request
     * @return
     */
    @PostMapping("/wxBatchExist")
    @Permission(action = Action.Skip)
    public BaseResponse<DoQueryWxBatchExistResultBO> wxBatchExist(@Decrypt BaseSecurityProtocol decryptData,
                                                                  HttpServletRequest request) {

        //检查接口商户是否允许调用当前接口
        checkEnableQuery(decryptData);

        Object planTextObj = decryptData.getPlainTextObj();
        RCWxBatchExistRequest requestData = BeanUtil.toBean(planTextObj, RCWxBatchExistRequest.class);
        log.info("do recharge info = {}", requestData);

        requestData.setBusinessCode(decryptData.getBusinessCode());
        ChannelWxActivityDTO channelWxActivityDTO = channelWxActivityService.selectIsRSBatchId(requestData.getBatchId());



        DoQueryWxBatchExistResultBO resultBO = new DoQueryWxBatchExistResultBO();
        resultBO.setExist(Objects.nonNull(channelWxActivityDTO));
        return successResponse(resultBO);
    }

    private void checkEnableQuery(BaseSecurityProtocol decryptData) {
        boolean b = queryEnableQuery(decryptData.getBusinessCode());
        if (!b) {
            throw new RechargeException(RECHARGE_QUERY_BATCH_NOT_SUPPORT);
        }
    }


    public boolean queryEnableQuery(String businessCode) {
        PSCSystemConfigBO pscSystemConfigBO = pscSystemConfigOpenService.getByCode(sysId, RCAppConst.RC_WX_BATCH_INFO_ENABLE_QUERY_BUSSINESS_CODE);
        String configValue = pscSystemConfigBO.getConfigValue();
        List<String> businessCodes = JSON.parse(configValue, new TypeReference<List<String>>() {
        });
        return businessCodes.contains(businessCode);

    }
}
