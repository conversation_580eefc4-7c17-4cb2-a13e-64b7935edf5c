package cn.joysim.cloud.rc.controller.open;


import cn.hutool.core.date.DateUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Decrypt;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.exception.BaseException;
import cn.joysim.cloud.common.model.dto.BaseSecurityProtocol;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.common.exception.PsbcException;
import cn.joysim.cloud.rc.common.utils.IpWhite;
import cn.joysim.cloud.rc.common.utils.psbc.SerialNoUtil;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.common.OpenApiMessageHead;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.common.OpenApiMessagePlainText;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.common.OpenApiResponseMessage;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.common.OpenApiResponseMessageHead;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.request.PsbcImportCouponCodeRequest;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.request.PsbcSendCouponRequest;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.response.PsbcImportCouponCodeResponse;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.response.PsbcSendCouponResponse;
import cn.joysim.cloud.rc.model.dto.ChannelProxyInfoDTO;
import cn.joysim.cloud.rc.service.ChannelProxyInfoService;
import cn.joysim.cloud.rc.service.ConfigDictService;
import cn.joysim.cloud.rc.service.PsbcCouponCodeImportService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/api/psbcTest/recharge")
@Slf4j
public class PsbcRechargeTestController {

    @Resource
    private PsbcCouponCodeImportService psbcCouponCodeImportService;


    @Resource
    private ConfigDictService configDictService;

    @Resource
    private ChannelProxyInfoService channelProxyInfoService;

    //请求接口importCouponCode
    @RequestMapping("/importCouponCode")
    @Permission(action = Action.Skip)
    @SneakyThrows
    public OpenApiResponseMessage<PsbcImportCouponCodeResponse> importCouponCode(@Decrypt BaseSecurityProtocol decryptData,
                                                                                 HttpServletRequest servletRequest) {

        String plainTextJson = decryptData.getPlainTextJson();


        log.info("收到邮储权益中心测试接口券码导入通知请求明文：{}", plainTextJson);

        OpenApiMessagePlainText request = JSON.parse(plainTextJson, OpenApiMessagePlainText.class);

        OpenApiMessageHead head = JSON.parse(request.getHead(), OpenApiMessageHead.class);
        String merchantId = head.getMerchantId();
        verifyIP(servletRequest,head.getMerchantId());
        try {


            PsbcImportCouponCodeRequest body = JSON.parse(request.getBody(),PsbcImportCouponCodeRequest.class);
            psbcCouponCodeImportService.saveCouponCodeImport(merchantId,body);

            OpenApiResponseMessage<PsbcImportCouponCodeResponse> responseValue = new OpenApiResponseMessage<PsbcImportCouponCodeResponse>();
            PsbcImportCouponCodeResponse psbcImportCouponCodeResponse = new PsbcImportCouponCodeResponse();
            psbcImportCouponCodeResponse.setRespCode(RCAppConst.PSBC_SUCCESS_CODE);
            psbcImportCouponCodeResponse.setRespMsg("处理成功");
            responseValue.setBody(psbcImportCouponCodeResponse);
            responseValue.setHead(getResponseHead(head));


            return responseValue;
        } catch (PsbcException | BaseException | ValidationException e) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcImportCouponCodeResponse> responseValue = new OpenApiResponseMessage<PsbcImportCouponCodeResponse>();
            PsbcImportCouponCodeResponse psbcSendCouponResponse = new PsbcImportCouponCodeResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg(e.getMessage());
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));
            return responseValue;
        }catch (Exception e) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcImportCouponCodeResponse> responseValue = new OpenApiResponseMessage<PsbcImportCouponCodeResponse>();
            PsbcImportCouponCodeResponse psbcSendCouponResponse = new PsbcImportCouponCodeResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg("处理失败");
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));
            return responseValue;
        }



    }

    private static OpenApiResponseMessageHead getResponseHead(OpenApiMessageHead head) {
        OpenApiResponseMessageHead responseHead = new OpenApiResponseMessageHead();
        responseHead.setPartnerTxSriNo(SerialNoUtil.getSerialNo());
        responseHead.setMethod(head.getMethod());
        responseHead.setVersion(head.getVersion());
        responseHead.setMerchantId(head.getMerchantId());
        responseHead.setAppID(head.getAppID());
        responseHead.setReqTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        responseHead.setReserve("");
        return responseHead;
    }


    private void verifyIP(HttpServletRequest request, String proxyCode) {
        String realIp = IpAddrUtil.getRealIp(request);
        //查询下游商户信息
        ChannelProxyInfoDTO proxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(proxyCode);
        if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
            throw new PsbcException(RCAppConst.PSBC_ERROR_CODE,"商户信息不存在");
        }
        //白名单校验
        String[] ips = org.springframework.util.StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
        if (null == ips || !IpWhite.isIpInWhitelist(realIp,ips)) {
            throw new PsbcException(RCAppConst.PSBC_ERROR_CODE,"IP白名单不匹配");
        }
    }


    //请求接口sendCoupon
    @RequestMapping("/sendCoupon")
    @Permission(action = Action.Skip)
    @SneakyThrows
    public OpenApiResponseMessage<PsbcSendCouponResponse> sendCoupon(@Decrypt BaseSecurityProtocol decryptData, HttpServletRequest servletRequest) {


        String reqMsg = decryptData.getPlainTextJson();

        log.info("收到券码导入通知请求明文：{}", reqMsg);

        OpenApiMessagePlainText requestData = JSON.parse(reqMsg, OpenApiMessagePlainText.class);
        OpenApiMessageHead head = JSON.parse(requestData.getHead(), OpenApiMessageHead.class);
        verifyIP(servletRequest,head.getMerchantId());
        ChannelProxyInfoDTO channelProxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(head.getMerchantId());
        try {


            PsbcSendCouponRequest request = JSON.parse(requestData.getBody(), PsbcSendCouponRequest.class);
            List<PsbcSendCouponResponse.VoucherResult> result= psbcCouponCodeImportService.sendCoupon(channelProxyInfoDTO.getId(),head.getPartnerTxSriNo(),request);

            OpenApiResponseMessage<PsbcSendCouponResponse> responseValue = new OpenApiResponseMessage<PsbcSendCouponResponse>();
            PsbcSendCouponResponse psbcSendCouponResponse = new PsbcSendCouponResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_SUCCESS_CODE);
            psbcSendCouponResponse.setRespMsg("处理成功");
            HashMap<String, Object> map = new HashMap<>();
            map.put("vouchersResult", result);
            psbcSendCouponResponse.setData(JSON.toJSONString(map));
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));



            log.info("加密响应报文response: {}", responseValue);
            return responseValue;
        } catch (PsbcException | BaseException | ValidationException e ) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcSendCouponResponse> responseValue = new OpenApiResponseMessage<PsbcSendCouponResponse>();
            PsbcSendCouponResponse psbcSendCouponResponse = new PsbcSendCouponResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg(e.getMessage());
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));
            log.info("加密响应报文response: {}", responseValue);
            return responseValue;
        }catch (Exception e) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcSendCouponResponse> responseValue = new OpenApiResponseMessage<PsbcSendCouponResponse>();
            PsbcSendCouponResponse psbcSendCouponResponse = new PsbcSendCouponResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg("处理失败");
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));

            return responseValue;
        }


    }
}
