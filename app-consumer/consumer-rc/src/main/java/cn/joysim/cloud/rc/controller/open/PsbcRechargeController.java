package cn.joysim.cloud.rc.controller.open;


import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.exception.BaseException;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.common.exception.PsbcException;
import cn.joysim.cloud.rc.common.utils.IpWhite;
import cn.joysim.cloud.rc.common.utils.psbc.CallbackUtil;
import cn.joysim.cloud.rc.common.utils.psbc.SerialNoUtil;
import cn.joysim.cloud.rc.common.utils.psbc.config.DictPsbcConfigDTO;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.common.*;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.request.PsbcImportCouponCodeRequest;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.request.PsbcSendCouponRequest;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.response.PsbcBaseResponse;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.response.PsbcImportCouponCodeResponse;
import cn.joysim.cloud.rc.common.utils.psbc.pojo.response.PsbcSendCouponResponse;
import cn.joysim.cloud.rc.model.dto.ChannelProxyInfoDTO;
import cn.joysim.cloud.rc.service.ChannelProxyInfoService;
import cn.joysim.cloud.rc.service.ConfigDictService;
import cn.joysim.cloud.rc.service.PsbcCouponCodeImportService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/api/psbc/recharge")
@Slf4j
public class PsbcRechargeController {

    @Resource
    private PsbcCouponCodeImportService psbcCouponCodeImportService;


    @Resource
    private ConfigDictService configDictService;

    @Resource
    private ChannelProxyInfoService channelProxyInfoService;

    //请求接口importCouponCode
    @RequestMapping("/importCouponCode")
    @Permission(action = Action.Skip)
    @SneakyThrows
    public OpenApiResponse importCouponCode(HttpServletRequest servletRequest) {

        String data = ServletUtil.getBody(servletRequest);
        log.info("收到券码导入通知请求密文：{}", data);

        //
        DictPsbcConfigDTO configObj = configDictService.getConfigObj(RCAppConst.RC_DICT_PSBC_CONFIG_KEY, DictPsbcConfigDTO.class);
        String reqMsg = CallbackUtil.requestDecrypt(configObj.getMerchantId(), configObj.getPrivateKey(), configObj.getSopPublicKey(), data);
        log.info("收到券码导入通知请求明文：{}", reqMsg);

        //解析报文失败时
        if (StringUtils.isEmpty(reqMsg)) {
            if (StringUtils.isEmpty(reqMsg)) {
                OpenApiMessage<PsbcBaseResponse> responseValue = new OpenApiMessage<PsbcBaseResponse>();
                PsbcBaseResponse psbcBaseResponse = new PsbcBaseResponse();
                psbcBaseResponse.setRespCode("900010");
                psbcBaseResponse.setRespMsg("报文解析异常");
                responseValue.setBody(psbcBaseResponse);
                OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
                log.info("加密响应报文response: {}", response);
                return response;
            }
        }

        OpenApiMessagePlainText request = JSON.parse(reqMsg, OpenApiMessagePlainText.class);

        OpenApiMessageHead head = JSON.parse(request.getHead(), OpenApiMessageHead.class);
        String merchantId = head.getMerchantId();
        verifyIP(servletRequest,head.getMerchantId());
        try {


            PsbcImportCouponCodeRequest body = JSON.parse(request.getBody(),PsbcImportCouponCodeRequest.class);
            psbcCouponCodeImportService.saveCouponCodeImport(merchantId,body);

            OpenApiResponseMessage<PsbcImportCouponCodeResponse> responseValue = new OpenApiResponseMessage<PsbcImportCouponCodeResponse>();
            PsbcImportCouponCodeResponse psbcImportCouponCodeResponse = new PsbcImportCouponCodeResponse();
            psbcImportCouponCodeResponse.setRespCode(RCAppConst.PSBC_SUCCESS_CODE);
            psbcImportCouponCodeResponse.setRespMsg("处理成功");
            responseValue.setBody(psbcImportCouponCodeResponse);
            responseValue.setHead(getResponseHead(head));

            OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
            log.info("加密响应报文response: {}", com.alibaba.fastjson.JSONObject.toJSONString(response));
            return response;
        } catch (PsbcException | BaseException | ValidationException e ) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcImportCouponCodeResponse> responseValue = new OpenApiResponseMessage<PsbcImportCouponCodeResponse>();
            PsbcImportCouponCodeResponse psbcSendCouponResponse = new PsbcImportCouponCodeResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg(e.getMessage());
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));
            OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
            return response;
        }catch (Exception e) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcImportCouponCodeResponse> responseValue = new OpenApiResponseMessage<PsbcImportCouponCodeResponse>();
            PsbcImportCouponCodeResponse psbcSendCouponResponse = new PsbcImportCouponCodeResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg("处理失败");
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));
            OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
            return response;
        }



    }

    private static OpenApiResponseMessageHead getResponseHead(OpenApiMessageHead head) {
        OpenApiResponseMessageHead responseHead = new OpenApiResponseMessageHead();
        responseHead.setPartnerTxSriNo(SerialNoUtil.getSerialNo());
        responseHead.setMethod(head.getMethod());
        responseHead.setVersion(head.getVersion());
        responseHead.setMerchantId(head.getMerchantId());
        responseHead.setAppID(head.getAppID());
        responseHead.setReqTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        responseHead.setReserve("");
        return responseHead;
    }


    private void verifyIP(HttpServletRequest request, String proxyCode) {
        String realIp = IpAddrUtil.getRealIp(request);
        //查询下游商户信息
        ChannelProxyInfoDTO proxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(proxyCode);
        if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
            throw new PsbcException(RCAppConst.PSBC_ERROR_CODE,"商户信息不存在");
        }
        //白名单校验
        String[] ips = org.springframework.util.StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
        if (null == ips || !IpWhite.isIpInWhitelist(realIp,ips)) {
            throw new PsbcException(RCAppConst.PSBC_ERROR_CODE,"IP白名单不匹配");
        }
    }


    //请求接口sendCoupon
    @RequestMapping("/sendCoupon")
    @Permission(action = Action.Skip)
    @SneakyThrows
    public OpenApiResponse sendCoupon(HttpServletRequest servletRequest) {


        String data = ServletUtil.getBody(servletRequest);
        log.info("收到发券通知请求密文：{}", data);

        //
        DictPsbcConfigDTO configObj = configDictService.getConfigObj(RCAppConst.RC_DICT_PSBC_CONFIG_KEY, DictPsbcConfigDTO.class);
        //Long proxyId = configObj.getProxyId();


        String reqMsg = CallbackUtil.requestDecrypt(configObj.getMerchantId(), configObj.getPrivateKey(), configObj.getSopPublicKey(), data);
        log.info("收到发券通知请求明文：{}", reqMsg);

        //解析报文失败时
        if (StringUtils.isEmpty(reqMsg)) {
            if (StringUtils.isEmpty(reqMsg)) {
                OpenApiMessage<PsbcBaseResponse> responseValue = new OpenApiMessage<PsbcBaseResponse>();
                PsbcBaseResponse psbcBaseResponse = new PsbcBaseResponse();
                psbcBaseResponse.setRespCode("900010");
                psbcBaseResponse.setRespMsg("报文解析异常");
                responseValue.setBody(psbcBaseResponse);
                OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
                log.info("加密响应报文response: {}", response);
                return response;
            }
        }

        OpenApiMessagePlainText requestData = JSON.parse(reqMsg, OpenApiMessagePlainText.class);
        OpenApiMessageHead head = JSON.parse(requestData.getHead(), OpenApiMessageHead.class);
        verifyIP(servletRequest,head.getMerchantId());

        ChannelProxyInfoDTO channelProxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(head.getMerchantId());
        try {


            PsbcSendCouponRequest request = JSON.parse(requestData.getBody(), PsbcSendCouponRequest.class);
            List<PsbcSendCouponResponse.VoucherResult> result= psbcCouponCodeImportService.sendCoupon(channelProxyInfoDTO.getId(),head.getPartnerTxSriNo(),request);

            OpenApiResponseMessage<PsbcSendCouponResponse> responseValue = new OpenApiResponseMessage<PsbcSendCouponResponse>();
            PsbcSendCouponResponse psbcSendCouponResponse = new PsbcSendCouponResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_SUCCESS_CODE);
            psbcSendCouponResponse.setRespMsg("处理成功");
            HashMap<String, Object> map = new HashMap<>();
            map.put("vouchersResult", result);
            psbcSendCouponResponse.setData(JSON.toJSONString(map));
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));


            OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
            log.info("加密响应报文response: {}", response);
            return response;
        } catch (PsbcException | BaseException | ValidationException e ) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcSendCouponResponse> responseValue = new OpenApiResponseMessage<PsbcSendCouponResponse>();
            PsbcSendCouponResponse psbcSendCouponResponse = new PsbcSendCouponResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg(e.getMessage());
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));
            OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
            return response;
        }catch (Exception e) {
            log.error("券码发放失败", e);
            OpenApiResponseMessage<PsbcSendCouponResponse> responseValue = new OpenApiResponseMessage<PsbcSendCouponResponse>();
            PsbcSendCouponResponse psbcSendCouponResponse = new PsbcSendCouponResponse();
            psbcSendCouponResponse.setRespCode(RCAppConst.PSBC_ERROR_CODE);
            psbcSendCouponResponse.setRespMsg("处理失败");
            responseValue.setBody(psbcSendCouponResponse);
            responseValue.setHead(getResponseHead(head));
            OpenApiResponse response = CallbackUtil.responseEncrypt(configObj.getMerchantId(), configObj.getPrivateKey(),configObj.getPublicKey(), configObj.getSopPublicKey(), JSONUtil.parseObj(responseValue));
            return response;
        }


    }

    public static void main(String[] args) {
        String str = "{\"head\":\"{\\\"appID\\\":\\\"PSBC\\\",\\\"merchantId\\\":\\\"testMerchant001\\\",\\\"method\\\":\\\"points.standardCouponIssueNotice\\\",\\\"partnerTxSriNo\\\":\\\"202503261838216977205369\\\",\\\"reqTime\\\":\\\"20250326183821\\\",\\\"version\\\":\\\"1\\\"}\",\"body\":\"{\\\"activityId\\\":\\\"psbc_mock_alipay_0001\\\",\\\"bizNo\\\":\\\"111111111\\\",\\\"showInfo\\\":\\\"CARD_VCH_NO\\\",\\\"svpverNo\\\":\\\"IMPORT_VOUCHER-GZJX\\\",\\\"templateName\\\":\\\"广州景心导入联调CBY\\\",\\\"totalNum\\\":\\\"100\\\",\\\"destroyFlag\\\":\\\"01\\\",\\\"vouchersAmt\\\":\\\"1.00\\\",\\\"templateId\\\":\\\"2025032615383490710111540210000208644875\\\"}\"}";
    }
}
