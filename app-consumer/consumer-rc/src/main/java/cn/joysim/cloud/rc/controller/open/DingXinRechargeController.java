package cn.joysim.cloud.rc.controller.open;

import cn.hutool.extra.servlet.ServletUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.common.exception.DingXinException;
import cn.joysim.cloud.rc.common.exception.RechargeOrderInvalidException;
import cn.joysim.cloud.rc.common.exception.ShandongAllianceException;
import cn.joysim.cloud.rc.common.exception.status.DingXinStatusCode;
import cn.joysim.cloud.rc.common.utils.IpWhite;
import cn.joysim.cloud.rc.model.dto.ChannelProxyInfoDTO;
import cn.joysim.cloud.rc.model.dto.dingxin.DingXinRequestConfig;
import cn.joysim.cloud.rc.model.dto.dingxin.helper.DingXinCouponHelper;
import cn.joysim.cloud.rc.model.dto.dingxin.request.DingXinOrderQueryRequest;
import cn.joysim.cloud.rc.model.dto.dingxin.request.DingXinRechargeRequest;
import cn.joysim.cloud.rc.model.dto.dingxin.response.DingXinOrderQueryResponse;
import cn.joysim.cloud.rc.model.dto.dingxin.response.DingXinRechargeResponse;
import cn.joysim.cloud.rc.service.ChannelProxyInfoService;
import cn.joysim.cloud.rc.service.DingXinRechargeService;
import cn.joysim.cloud.rc.utils.ValidateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/api/dingxin/recharge")
@Slf4j
public class DingXinRechargeController {

    @Resource
    private DingXinRechargeService dingXinRechargeService;

    @Resource
    private ChannelProxyInfoService channelProxyInfoService;

    @SneakyThrows
    private void verifySign(Map<String, String> map) {

        String supplierNo = map.get("supplier_no");

        String providedSignature = map.get("sign");

        ChannelProxyInfoDTO proxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(supplierNo);
        if (Objects.isNull(proxyInfoDTO)) {
            log.info("商户编码不存在");
            throw new DingXinException(DingXinStatusCode.VENDOR_ACCOUNT_DOES_NOT_EXIST_OR_IS_INCORRECT);
        }

        DingXinRequestConfig dingXinRequestConfig = dingXinRechargeService.transformProxyId(supplierNo);

        if (Objects.isNull(dingXinRequestConfig)) {
            log.info("商户编码配置秘钥不存在");
            throw new DingXinException(DingXinStatusCode.VENDOR_ACCOUNT_DOES_NOT_EXIST_OR_IS_INCORRECT);
        }

        String generatedSignature = new DingXinCouponHelper(dingXinRequestConfig.getAppSecret()).generateSignature(map);
        log.info("提供的签名:{},获取的签名:{}",providedSignature,generatedSignature);
        if (!providedSignature.equals(generatedSignature)) {
            throw new DingXinException(DingXinStatusCode.SIGNATURE_ERROR);
        }

        Long timestamp = Long.valueOf(map.getOrDefault("timestamp", "0"));
        long currentTimeMillis = System.currentTimeMillis();

        long differenceInMillis = Math.abs(timestamp - currentTimeMillis);
        // 判断时间差是否超过3秒
        if (differenceInMillis > 180000) {
            log.info("请求超时,时间戳:请求时间戳:{},系统时间戳:{}",timestamp,currentTimeMillis);
            throw new DingXinException(DingXinStatusCode.REQUEST_TIMEOUT);
        }
    }


    /**
     * 下单充值
     *
     */
    @RequestMapping(value = "/doRecharge", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public DingXinRechargeResponse doRecharge(HttpServletRequest request) {
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        log.info("鼎信下单充值: {}",paramMap);

        DingXinRechargeRequest requestData = JSON.parse(JSON.toJSONString(paramMap), DingXinRechargeRequest.class);
        ValidateUtil.validate(requestData);

        //验证签名
        verifySign(paramMap);

        verifyIP(request,requestData.getSupplierNo());

        log.info("鼎信requestData: {}",requestData);

        DingXinRechargeResponse dingXinRechargeResponse = dingXinRechargeService.rechargeDingXin(requestData);

        return dingXinRechargeResponse;
    }


    @RequestMapping(value = "/query", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public DingXinOrderQueryResponse query(HttpServletRequest request) {
        Map<String, String> paramMap = ServletUtil.getParamMap(request);
        log.info("鼎信订单查询: {}",paramMap);

        DingXinOrderQueryRequest requestData = JSON.parse(JSON.toJSONString(paramMap), DingXinOrderQueryRequest.class);
        ValidateUtil.validate(requestData);

        //验证签名
        verifySign(paramMap);

        verifyIP(request,requestData.getSupplierNo());

        log.info("鼎信订单查询requestData: {}",requestData);


        DingXinOrderQueryResponse response=dingXinRechargeService.query(requestData);
        return response;

    }

    private void verifyIP(HttpServletRequest request, String proxyCode) {
        String realIp = IpAddrUtil.getRealIp(request);
        //查询下游商户信息
        ChannelProxyInfoDTO proxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(proxyCode);
        if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
            throw new DingXinException(DingXinStatusCode.VENDOR_ACCOUNT_DOES_NOT_EXIST_OR_IS_INCORRECT);
        }
        //白名单校验
        String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
        if (null == ips || !IpWhite.isIpInWhitelist(realIp,ips)) {
            throw new DingXinException(DingXinStatusCode.REQUEST_IP_IS_INCORRECT);
        }
    }

}
