package cn.joysim.cloud.rc.controller.open.exceptionHandler;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.exception.BaseServiceException;
import cn.joysim.cloud.common.model.vo.BaseResponse;
import cn.joysim.cloud.common.util.ResponseUtil;
import cn.joysim.cloud.rc.common.exception.ChannelProxyInfoException;
import cn.joysim.cloud.rc.common.exception.ChannelSupplierException;
import cn.joysim.cloud.rc.common.exception.ShandongAllianceException;
import cn.joysim.cloud.rc.common.exception.ShandongAllianceRechargeException;
import cn.joysim.cloud.rc.controller.open.ShandongBankAllianceCouponController;
import cn.joysim.cloud.rc.controller.open.ShandongBankAllianceRechargeController;
import cn.joysim.cloud.rc.filter.ParamsFilter;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.ShandongBaseResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.ShandongRechargeBaseResponse;
import cn.joysim.cloud.rc.utils.BindExceptionUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.ValidationException;
import java.util.Optional;


@Slf4j
@ControllerAdvice(assignableTypes = {ShandongBankAllianceRechargeController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ShandongBankAllianceRechargeExceptionHandler {


    @ExceptionHandler({ValidationException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse validationException(HttpServletRequest req, HttpServletResponse resp, ValidationException e) {
        resp.setContentType("application/json;charset=UTF-8");
        log.error("山东联盟下单异常:{}",e,e);
        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        r.setCode("000001");
        String appId = getAppId(req);
        r.setAppId(appId);

        String s = e.getMessage();
        r.setDesc(s);
        return r;
    }

    @ExceptionHandler({javax.validation.ValidationException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse validation2Exception(HttpServletRequest req, HttpServletResponse resp, javax.validation.ValidationException e) {
        resp.setContentType("application/json;charset=UTF-8");
        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        r.setCode("000001");
        String appId = getAppId(req);
        r.setAppId(appId);

        String s = e.getMessage();
        r.setDesc(s);
        return r;
    }

    @ExceptionHandler({BindException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse bindErrorHandler(HttpServletRequest req, HttpServletResponse resp, BindException e) {
        resp.setContentType("application/json;charset=UTF-8");
        log.error("山东联盟下单异常:{}",e,e);
        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        r.setCode("000001");
        String appId = getAppId(req);
        r.setAppId(appId);

        String s = BindExceptionUtil.handleArgumentErrorMsg(e.getBindingResult());
        r.setDesc(s);
        return r;
    }

    @ExceptionHandler({ShandongAllianceException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse shandongAllianceException(HttpServletRequest req, HttpServletResponse resp,ShandongAllianceException e) throws Exception {
        resp.setContentType("application/json;charset=UTF-8");
        log.error("山东联盟下单异常:{}",e,e);
        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        r.setCode("000001");
        String appId = getAppId(req);
        r.setAppId(appId);
        r.setDesc(e.getMessage());
        return r;
    }

    @ExceptionHandler({ShandongAllianceRechargeException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse shandongAllianceRechargeException(HttpServletRequest req, HttpServletResponse resp,ShandongAllianceRechargeException e) throws Exception {
        resp.setContentType("application/json;charset=UTF-8");
        log.error("山东联盟下单异常:{}",e,e);
        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        r.setCode(e.getCode());
        String appId = getAppId(req);
        r.setAppId(appId);
        r.setDesc(e.getMessage());
        return r;
    }

    @ExceptionHandler({BaseServiceException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse customServiceErrorHandler(HttpServletRequest req, HttpServletResponse resp, BaseServiceException e) throws Exception {
        resp.setContentType("application/json;charset=UTF-8");

        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        String appId = getAppId(req);
        r.setAppId(appId);
        r.setCode("000001");
        r.setDesc(StrUtil.format("基础配置异常[{}]",e.getCode()));

        return r;
    }




    @ExceptionHandler({ChannelProxyInfoException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse defaultErrorHandler(HttpServletRequest req, HttpServletResponse resp, ChannelProxyInfoException e) throws Exception {
        resp.setContentType("application/json;charset=UTF-8");

        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        String appId = getAppId(req);
        r.setAppId(appId);
        r.setCode("1");
        r.setDesc(e.getMessage());

        return r;
    }

    @ExceptionHandler({RpcException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse rpcErrorHandler(HttpServletRequest req, HttpServletResponse resp,RpcException e) {
        resp.setContentType("application/json;charset=UTF-8");
        log.error("RPC exception: {}", e.getMessage(), e);
        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        String appId = getAppId(req);
        r.setAppId(appId);
        r.setCode("1");
        return r;
    }

    @ExceptionHandler({ChannelSupplierException.class})
    @ResponseBody
    public ShandongRechargeBaseResponse defaultChannelSupplierException(HttpServletRequest req, HttpServletResponse resp, ChannelSupplierException e) throws Exception {
        resp.setContentType("application/json;charset=UTF-8");

        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        String appId = getAppId(req);
        r.setAppId(appId);
        r.setCode("1");
        r.setDesc(e.getMessage());

        return r;
    }


    @SneakyThrows
    private static String getAppId(HttpServletRequest req) {

        String appId = (String) req.getAttribute("appId");
        return appId;

    }


    @ExceptionHandler({Exception.class})
    @ResponseBody
    public ShandongRechargeBaseResponse defaultErrorHandler(HttpServletRequest req, HttpServletResponse resp, Exception e) throws Exception {
        resp.setContentType("application/json;charset=UTF-8");

        log.error("山东联盟下单异常:{}",e,e);
        ShandongRechargeBaseResponse r = new ShandongRechargeBaseResponse();
        String appId = getAppId(req);
        r.setAppId(appId);
        r.setCode("1");
        r.setDesc("系统异常");

        return r;
    }

}
