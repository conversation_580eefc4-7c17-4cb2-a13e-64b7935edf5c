package cn.joysim.cloud.rc.controller.open;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.controller.BaseController;
import cn.joysim.cloud.common.exception.BaseException;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.psc.service.PSCSystemConfigOpenService;
import cn.joysim.cloud.rc.common.exception.RechargeException;
import cn.joysim.cloud.rc.common.exception.RsRechargeException;
import cn.joysim.cloud.rc.common.exception.SpcRechargeException;
import cn.joysim.cloud.rc.common.exception.status.RechargeStatusCode;
import cn.joysim.cloud.rc.common.exception.status.RsRechargeStatusCode;
import cn.joysim.cloud.rc.common.exception.status.SpcRechargeStatusCode;
import cn.joysim.cloud.rc.common.model.pojo.enums.CouponCallBackCreatorType;
import cn.joysim.cloud.rc.common.model.pojo.enums.RechargeProductType;
import cn.joysim.cloud.rc.common.utils.IpWhite;
import cn.joysim.cloud.rc.controller.request.*;
import cn.joysim.cloud.rc.model.bo.DoRechargeBO;
import cn.joysim.cloud.rc.model.bo.SpcConfigBO;
import cn.joysim.cloud.rc.model.bo.SpcRechargeResultBO;
import cn.joysim.cloud.rc.model.dto.ChannelProxyInfoDTO;
import cn.joysim.cloud.rc.model.dto.SpcProductDTO;
import cn.joysim.cloud.rc.model.dto.SpcProductDetailDTO;
import cn.joysim.cloud.rc.model.dto.SpcProductForSpcDTO;
import cn.joysim.cloud.rc.model.dto.request.ProxyDoRechargeMobilePhoneRequest;
import cn.joysim.cloud.rc.model.dto.request.ProxyDoRechargeOilCardRequest;
import cn.joysim.cloud.rc.service.*;
import cn.joysim.cloud.rc.service.virtualCoupon.RechargeVirtualCouponOrderService;
import cn.joysim.cloud.rc.utils.SpcCheckUtil;
import cn.joysim.cloud.rc.utils.ValidateParamUtil;
import cn.joysim.cloud.rc.utils.ValidateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: SPC下单接口
 * @Author: LiuHW
 * @date: 2023/10/16 9:12
 */
@RestController
@RequestMapping("/spc/v1")
@Slf4j
public class SpcRechargeController extends BaseController {

    @Value("${common.application.id}")
    private Long sysId;

    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCSystemConfigOpenService pscSystemConfigOpenService;

    @Resource
    private SpcRechargeService spcRechargeService;

    @Resource
    private SpcProductService spcProductService;

    @Resource
    private SpcProductDetailService spcProductDetailService;

    @Resource
    private RechargeService rechargeService;

    @Resource
    private ChannelProxyInfoService proxyInfoService;

    @Resource
    private Validator validator;

    @Resource
    private RechargeVirtualCouponOrderService virtualCouponOrderService;

    /**
     * 1.SPC下单
     * 2.SPC要求的接口是一个商品对应多个面值，充值中心是一个商品对应一个面值，下单传的是商品编码和面值编码
     * 3.SPC面值编码关联对应的面值，下单的时候通过编码找到对应的面值
     * 4.话费，油卡，卡密给到贵州邮储的产品编码和面值编码，在下单过程中，找到对应的面值，
     * 5.对于卡券来说，面值编码<等于>商户商品编码
     *
     * @param rechargeRequest
     * @param servletRequest
     * @return
     */
    @PostMapping("/charge")
    @Permission(action = Action.Skip)
    public SpcRechargeResultBO spcRecharge(@Valid @RequestBody SpcRechargeRequest rechargeRequest, HttpServletRequest servletRequest) {
        SpcRechargeResultBO result = new SpcRechargeResultBO();
        long startTime = System.currentTimeMillis();
        log.info("spc下单开始:{}", DateUtil.formatDateTime(new Date(startTime)));
        log.info("spc下单请求:{}", JSONUtil.toJsonStr(rechargeRequest));
        try {
            SpcConfigBO spcConfig = spcRechargeService.getSpcConfigBO();
            //校验appKey
            SpcCheckUtil.checkAppKey(spcConfig.getAppKey(), rechargeRequest.getAppKey());
            //解密
            String data = SpcCheckUtil.decrypt(rechargeRequest.getData(), spcConfig.getAesKey(), spcConfig.getIvKey());
            log.info("spc下单解密:{}", JSONUtil.toJsonStr(data));
            //验签
            SpcCheckUtil.verifySign(spcConfig.getAppKey(), rechargeRequest.getData(), rechargeRequest.getTimestamp(), rechargeRequest.getSign());
            //校参
            ProxySpcRechargeRequest spcRechargeRequest = JSONUtil.toBean(data, ProxySpcRechargeRequest.class);
            log.info("spc下单参数:{}", JSONUtil.toJsonStr(data));
            StringBuilder sb = ValidateParamUtil.commonValidate(spcRechargeRequest, validator);
            if (sb.length() > 0) {
                throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_PARAM_IS_INCOMPLETE);
            }

            //SPC商品
            SpcProductDTO spcProductDTO = spcProductService.getSpcProduct(spcRechargeRequest.getProNo());
            if (spcProductDTO == null){
                throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_PRODUCT_ERROR);
            }
            //SPC可用面值
            SpcProductDetailDTO spcProductDetailDTO = spcProductDetailService.getSpcProductDetailDTO(spcProductDTO.getId(), spcRechargeRequest.getMvNo());
            if (spcProductDetailDTO == null) {
                throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_MV_ERROR);
            }
            DoRechargeBO doRechargeDTO = new DoRechargeBO();
            doRechargeDTO.setIpAddress(IpAddrUtil.getRealIp(servletRequest));
            StringBuilder errorSb = new StringBuilder();
            RechargeProductType productType = spcProductDTO.getProductType();
            if (RechargeProductType.RECHARGE_MOBILE_PHONE == productType) {
                ProxyDoRechargeMobilePhoneRequest mobilePhoneRequestData = new ProxyDoRechargeMobilePhoneRequest();
                mobilePhoneRequestData.setBusinessCode(spcConfig.getBusinessCode());
                mobilePhoneRequestData.setPhoneNo(spcRechargeRequest.getChargeNumber());
                mobilePhoneRequestData.setDenomination(spcProductDetailDTO.getMoney());
                mobilePhoneRequestData.setReceiveMessagePhoneNo(spcRechargeRequest.getChargeNumber());
                mobilePhoneRequestData.setCustomBizId(spcRechargeRequest.getMerOrderId());
                mobilePhoneRequestData.setCallbackUrl(spcConfig.getCallBackUrl());
                ValidateUtil.commonValidate(mobilePhoneRequestData, errorSb);
                if (StringUtils.hasText(errorSb.toString())) {
                    throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_PARAM_IS_INCOMPLETE);
                }
                BeanUtil.copyProperties(mobilePhoneRequestData, doRechargeDTO);
            } else if (RechargeProductType.RECHARGE_OIL_CARD == productType) {
                ProxyDoRechargeOilCardRequest oilCardRequestData = new ProxyDoRechargeOilCardRequest();
                oilCardRequestData.setBusinessCode(spcConfig.getBusinessCode());
                //油卡面值校验处理(如100.0000转成100)
                oilCardRequestData.setDenomination(new BigDecimal(spcProductDetailDTO.getMoney().stripTrailingZeros().toPlainString()));
                oilCardRequestData.setOilCardNo(spcRechargeRequest.getChargeNumber());
                oilCardRequestData.setCustomBizId(spcRechargeRequest.getMerOrderId());
                oilCardRequestData.setCallbackUrl(spcConfig.getCallBackUrl());
                ValidateUtil.commonValidate(oilCardRequestData, errorSb);
                if (StringUtils.hasText(errorSb.toString())) {
                    throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_PARAM_IS_INCOMPLETE);
                }
                BeanUtil.copyProperties(oilCardRequestData, doRechargeDTO);
            } else if (RechargeProductType.RECHARGE_CARD_CIPHER == productType) {
                ProxyDoRechargeCardCipherRequest cardCipherRequestData = new ProxyDoRechargeCardCipherRequest();
                cardCipherRequestData.setBusinessCode(spcConfig.getBusinessCode());
                cardCipherRequestData.setDenomination(spcProductDetailDTO.getMoney());
                cardCipherRequestData.setProductId(spcProductDetailDTO.getMvNo());
                cardCipherRequestData.setCustomBizId(spcRechargeRequest.getMerOrderId());
                cardCipherRequestData.setReceivePhoneNo(spcRechargeRequest.getChargeNumber());
                cardCipherRequestData.setCallbackUrl(spcConfig.getCallBackUrl());
                cardCipherRequestData.setSpType(spcProductDTO.getSpType());
                if (StringUtils.hasText(errorSb.toString())) {
                    throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_PARAM_IS_INCOMPLETE);
                }
                BeanUtil.copyProperties(cardCipherRequestData, doRechargeDTO);
            } else if (RechargeProductType.RECHARGE_VIRTUAL_COUPON == productType) {
                ProxyDoRechargeVirtualCouponRequest virtualCouponRequestData = new ProxyDoRechargeVirtualCouponRequest();
                virtualCouponRequestData.setBusinessCode(spcConfig.getBusinessCode());
                virtualCouponRequestData.setDenomination(spcProductDetailDTO.getMoney());
                virtualCouponRequestData.setAccountNo(spcRechargeRequest.getChargeNumber());
                virtualCouponRequestData.setCustomBizId(spcRechargeRequest.getMerOrderId());
                virtualCouponRequestData.setProductId(spcProductDetailDTO.getMvNo());
                virtualCouponRequestData.setCallbackUrl(spcConfig.getCallBackUrl());
                virtualCouponRequestData.setSpType(spcProductDTO.getSpType());
                virtualCouponRequestData.setForceCouponCallBackCreatorType(CouponCallBackCreatorType.SPC);
                if (StringUtils.hasText(errorSb.toString())) {
                    throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_PARAM_IS_INCOMPLETE);
                }
                BeanUtil.copyProperties(virtualCouponRequestData, doRechargeDTO);
            } else {
                throw new RechargeException(RechargeStatusCode.PRODUCT_TYPE_ERROR);
            }
            //下单走原接口逻辑
            doRechargeDTO.setProductType(productType.getCode());
            rechargeService.doRecharge(doRechargeDTO);

            long endTime = System.currentTimeMillis();
            log.info("spc下单结束:{}", DateUtil.formatDateTime(new Date(endTime)));
            log.info("spc下单总计耗时:{}", endTime - startTime);
            log.info("spc下单返回:{}", JSONUtil.toJsonStr(result));

            result.setCode("0000");
            result.setMsg(SpcRechargeStatusCode.SUCCESS.getText());
        } catch (BaseException e) {
            log.error("spc下单异常:{}", e.getMessage());
            result.setCode(e.getStatus().getCode().toString());
            result.setMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 1.SPC商品查询
     *
     * @param productRequest
     * @return
     */
    @PostMapping("/product")
    @Permission(action = Action.Skip)
    public SpcRechargeResultBO spcProduct(@Valid @RequestBody SpcProductRequest productRequest,HttpServletRequest servletRequest) {
        SpcRechargeResultBO result = new SpcRechargeResultBO();
        log.info("spc商品查询请求:{}", JSONUtil.toJsonStr(productRequest));
        try {
            SpcConfigBO spcConfig = spcRechargeService.getSpcConfigBO();
            //校验appKey
            if (!StrUtil.equals(spcConfig.getAppKey(), productRequest.getAppKey())) {
                throw new SpcRechargeException(SpcRechargeStatusCode.SPC_RECHARGE_UNAUTH_APPKEY);
            }
            //解密
            String data = SpcCheckUtil.decrypt(productRequest.getData(), spcConfig.getAesKey(), spcConfig.getIvKey());
            log.info("spc查询解密:{}", JSONUtil.toJsonStr(data));
            //验签
            SpcCheckUtil.verifySign(spcConfig.getAppKey(), productRequest.getData(), productRequest.getTimestamp(), productRequest.getSign());
            //查询商户信息
            ChannelProxyInfoDTO proxyInfoDTO = proxyInfoService.getInfoByCodeCache(spcConfig.getBusinessCode());
            if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
                throw new RsRechargeException(RsRechargeStatusCode.PROXY_INFO_IS_NOT_EXIST);
            }
            //白名单校验
            String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
            log.info("clientIpAddress = {}, whiteList = {}", IpAddrUtil.getRealIp(servletRequest), ips);
            if (null == ips || !IpWhite.isIpInWhitelist(IpAddrUtil.getRealIp(servletRequest),ips)) {
                throw new RsRechargeException(RsRechargeStatusCode.IP_WHILE_LIST_ERROR);
            }
            List<SpcProductForSpcDTO> productList = spcRechargeService.spcProduct(data);
            result.setCode("0000");
            result.setMsg(SpcRechargeStatusCode.SUCCESS.getText());
            result.setData(productList);
        } catch (SpcRechargeException e) {
            log.error("SPC查询商品失败:{},{}", e, e.getMessage());
            result.setCode(e.getStatus().getCode().toString());
            result.setMsg(e.getMessage());
        }
        return result;
    }

//    public void commonValidate(BaseValidateRequest validateRequest, StringBuilder errorSb) {
//        Set<ConstraintViolation<BaseValidateRequest>> violationSet = validator.validate(validateRequest);
//        for (ConstraintViolation<BaseValidateRequest> model : violationSet) {
//            errorSb.append("[").append(model.getMessage()).append("]");
//        }
//    }

}
