package cn.joysim.cloud.rc.controller.admin.base;

import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.joysim.cloud.common.controller.BaseController;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

/**
 * hutool导出基础类
 *
 * <AUTHOR>
 * @version 2022-10-08
 */
public abstract class HutoolBaseExportController extends BaseController {

    /**
     * 日志对象
     */
    protected Logger logger = LoggerFactory.getLogger(getClass());

    protected void writeExcel(String fileName, ExcelWriter writer, List exportList, HttpServletResponse response) {
        writer.write(exportList, Boolean.TRUE);
        writer.autoSizeColumnAll();

        Sheet sheet = writer.getSheet();
        // BigExcelWriter 内存默认只存最后100条数据，其他写进磁盘，因此需要取最后一行
        Row row = sheet.getRow(sheet.getLastRowNum());
        int rowColumnNum = row.getLastCellNum();
        for (int i = 0; i < rowColumnNum; i++) {
            // 解决自动设置列宽中文失效的问题
            sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 17 / 10);
        }

        String dateStr = DateUtil.format(new Date(), "yyyyMMddHHmmss");
        String codedFileName = null;
        String formatFileName = String.format(fileName, dateStr);
        try {
            codedFileName = URLEncoder.encode(formatFileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("导出excel => {} 异常，{}", formatFileName, e);
        }
        response.setHeader("content-disposition", "attachment;filename=" + codedFileName);
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, Boolean.TRUE);
        } catch (IOException e) {
            logger.warn("导出excel => {} 关闭流异常，{}", formatFileName, e);
        } finally {
            writer.close();
        }
    }
}
