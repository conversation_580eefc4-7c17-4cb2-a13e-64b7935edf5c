package cn.joysim.cloud.rc.controller.open;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.controller.BaseController;
import cn.joysim.cloud.common.exception.status.GlobalStatusCode;
import cn.joysim.cloud.common.model.vo.BaseResponse;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.psc.service.PSCSystemConfigOpenService;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.model.bo.RsRechargeBO;
import cn.joysim.cloud.rc.model.dto.ShandongAllianceProxyDTO;
import cn.joysim.cloud.rc.model.dto.dingxin.helper.DingXinCouponHelper;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.RechargeEncryptedResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.ShandongRechargeBaseResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.helper.ShandongBankAllianceCouponHelper;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.helper.ShandongBankAllianceRechargeHelper;
import cn.joysim.cloud.rc.service.ShandongAllianceProxyService;
import cn.joysim.cloud.rc.utils.RSAHelper;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import java.io.InputStream;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 加密接口仅测试环境生效
 *
 * <AUTHOR>
 */
@Profile({"dev", "uat","test"})
@RestController
@RequestMapping("/api/rc/recharge")
@Slf4j
public class RsUatEncryptRechargeController extends BaseController {

    @DubboReference(version = "1.0.0", group = "${common.application.psc.group}")
    private PSCSystemConfigOpenService configService;

    @Value("${common.application.id}")
    private Long sysId;

    private RSAPublicKey publicKey;

    private RSAPrivateKey privateKey;

    @Resource
    private ShandongAllianceProxyService shandongAllianceProxyService;

    @PostMapping("/encrypt")
    @Permission(action = Action.Skip)
    public BaseResponse<Map> encrypt(HttpServletRequest req) {
        HashMap map = new HashMap();

        try {
            String vendorNo = req.getParameter("vendorNo");
            if (StrUtil.isBlank(vendorNo)) {
                if(publicKey == null){
                    publicKey = RSAHelper.getPemRSAPublicKey(configService.getByCode(sysId, RCAppConst.JINGXIN_RONGSHU_PUBLIC_KEY).getConfigValue());
                }
                if(privateKey == null){
                    privateKey = RSAHelper.getPemRSAPrivateKey(configService.getByCode(sysId, RCAppConst.RONGSHU_PRIVATE_KEY).getConfigValue());
                }
            }else{
                String publicKeyValue = configService.getByCode(sysId, RCAppConst.JINGXIN_RONGSHU_PUBLIC_KEY.concat("_").concat(vendorNo)).getConfigValue();
                String privateKeyValue = configService.getByCode(sysId, RCAppConst.RONGSHU_PRIVATE_KEY.concat("_").concat(vendorNo)).getConfigValue();

//                publicKey = RSAHelper.getPemRSAPublicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDHBIStwZtFlJ9sWvX/AxunHeUBJ9KhfO7CAFcR0NDGwbhMnKz+MvaLGlZ77Z7ek9YzpiwDuDNaBMycz6UQ/An5Cohq7i7tW3bIfnQEQm2MNRQ0lP+asmHRuucDriSBRg8H+utbI52CCrD8DIm/vHnxDRrn184wctyN89pmypdqgwIDAQAB");
//
//                privateKey = RSAHelper.getPemRSAPrivateKey("MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIc9PErLzL0w4YjaHnYO5W+TRGU6/Y2MFNjzx+scuTepFhTbjKZ0ZXgf761rwNmIwk1rU4jWgDJgxWULw5loQ1ZOdQ8nYS3vqOdDXN22ebV+XvmXzredjInH6onCiEFOogca1CkunqlkctYtiDoHtRiSmdrtM5OMSkqZWinqo7ZxAgMBAAECgYAJS1QRb+AqQGm/lf3x7yQlfuF8c4lpaO8l2dsrdVXlVWGHHW+VIPq4PrmiGX2vdY6k83NSPCujSrNGNoDC0j+OXM6hCmE3FBzws7K0Bt7PnS3f/1l2Rsx8aGDq+XpJEhUICx/v5GfXeqmTgm/0HmgEcZO/QfRI3LpRkEsMkx8OgQJBAONjWcgvyP2uWLmvZlfawWgLbusG0buxXH+AOFHwDg3TtWPi7boRS6IRzOFO/iya6+UZc3UVQMkdzcC8SHjZRGUCQQCYQZM2JB/7TJXG3pccEokBGQuDxjbOJZEKuifs4qCW2nm4Trr7KRwjoFgwSiPZgMa7li7C8ZoYBe9IqspbwisdAkBG6XbYXT6bDkIRNkf+YfQq1FX06Z2CoR8ti/kEZI6ddUZ+LgjhG/+wPUjdgtr1YiLqwXMmMkiP78F4t78KIQeZAkEAjXvCNHl1TqxoMhj+CpQew+pmDNnQa9f05CcAmtwtpoD2wxJGJsaY43JJAPakQaWtBvwUEAs6ykAZj0lGKeZQ8QJABb7ocsVGdLlPCe/2oKdUuWCDS4U72DmLP1xz20nFzfYAuuch4WTz+8cYW8Gb0HvWLBkij8ov3aLiYXZnddBFPA==");

                log.info("publicKey: {}",publicKeyValue);
                log.info("privateKey: {}",privateKeyValue);
                publicKey = RSAHelper.getPemRSAPublicKey(publicKeyValue);
                privateKey = RSAHelper.getPemRSAPrivateKey(privateKeyValue);
            }

            //cn.joysim.cloud.rc.model.bo.RsRechargeBO
            //cn.joysim.cloud.rc.model.bo.RsDirectChargeBO
            String data = ServletUtil.getBody(req);
            log.info("加密原文: {}",data);
            //String data = JSONUtil.toJsonStr(request);
            Map<String, Object> params = JSONObject.parseObject(data, Map.class);
            Map<String, Object> sortedMap = params.entrySet().stream().filter(item-> Objects.nonNull(item.getValue())).sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
            String rsaData = RSAHelper.encrypt(publicKey, JSONUtil.toJsonStr(sortedMap));
            map.put("data",rsaData);
            String sign = RSAHelper.sign(JSONUtil.toJsonStr(sortedMap), privateKey);
            map.put("sign",sign);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return successResponse(map);
    }


    @PostMapping("/shandongCouponEncrypt")
    @Permission(action = Action.Skip)
    public BaseResponse<Map> shandongCouponEncrypt(@RequestHeader Map<String, String> headers, HttpServletRequest req) {
        HashMap map = new HashMap();

        try {
            String body = ServletUtil.getBody(req);
            ObjectMapper mapper = new ObjectMapper();
            String couponSignKey = headers.get("couponsignkey");


            Map<String, Object> parameter = mapper.readValue(body, TypeFactory.defaultInstance().constructMapType(Map.class, String.class, Object.class));
           log.info("参数:{}",parameter);

            String generatedSignature = new ShandongBankAllianceCouponHelper(couponSignKey).generateSignature(parameter);

            map.put("sign",generatedSignature);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return successResponse(map);
    }

    @PostMapping("/dingXinCouponEncrypt")
    @Permission(action = Action.Skip)
    public BaseResponse<Map> dingXinCouponEncrypt(@RequestHeader Map<String, String> headers, HttpServletRequest req) {
        HashMap map = new HashMap();

        try {

            String appSecret = headers.get("appsecret");
            Map<String, String> paramMap = ServletUtil.getParamMap(req);

            String generatedSignature = new DingXinCouponHelper(appSecret).generateSignature(paramMap);

            map.put("sign",generatedSignature);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return successResponse(map);
    }

    @PostMapping("/shandongRechargeEncrypt")
    @Permission(action = Action.Skip)
    public BaseResponse<ShandongRechargeBaseResponse> shandongRechargeEncrypt(@RequestHeader Map<String, String> headers,HttpServletRequest req) {

        try {
            String directRecharge3desKey = headers.get("directrecharge3deskey");
            String directRechargeSignKey = headers.get("directrechargesignkey");
            String appId = headers.get("appid");
            String transCode = headers.get("transcode");
            Optional<ShandongAllianceProxyDTO> byProxyId = shandongAllianceProxyService.getByProxyCode(appId);
            if (!byProxyId.isPresent()) {
                throw new ValidationException("商户秘钥不存在");
            }
            ShandongBankAllianceRechargeHelper helper = new ShandongBankAllianceRechargeHelper(directRecharge3desKey, directRechargeSignKey);
            String data = ServletUtil.getBody(req);

            String content = JSONUtil.parse(data).getByPath("transData",String.class);


            RechargeEncryptedResponse encryptedData = new RechargeEncryptedResponse();
            encryptedData.setTransData(content);
            encryptedData.setTransCode(transCode);
            encryptedData.setTimestamp(String.valueOf(System.currentTimeMillis()));
            encryptedData.setTransNonce(IdUtil.simpleUUID());
            encryptedData.setTransSign(helper.sign(encryptedData.getTransData(), encryptedData.getTransNonce(),encryptedData.getTimestamp()));


            log.info("响应加密信息#,requestData: {}",encryptedData);

            String encryptedDataValue = helper.encrypt(JSON.toJSONString(encryptedData));

            ShandongRechargeBaseResponse response = new ShandongRechargeBaseResponse();
            response.setAppId(appId);
            response.setEncryptData(encryptedDataValue);

            return successResponse(response);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @PostMapping("/shandongRechargeDecrypt")
    @Permission(action = Action.Skip)
    public BaseResponse<String> shandongRechargeDecrypt(@RequestHeader Map<String, String> headers,HttpServletRequest req) {

        try {
            String directRecharge3desKey = headers.get("directrecharge3deskey");
            String directRechargeSignKey = headers.get("directrechargesignkey");
            ShandongBankAllianceRechargeHelper helper = new ShandongBankAllianceRechargeHelper(directRecharge3desKey, directRechargeSignKey);
            String data = ServletUtil.getBody(req);
            cn.hutool.json.JSON json = JSONUtil.parse(data);
            String byPath = (String) json.getByPath("encryptData");
            if (StrUtil.isNotBlank(byPath)) {
                String decrypt = helper.decrypt(byPath);
                return successResponse(decrypt);
            }
            return successResponse(data);



        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * 1.荣数补单接口，如果存在丢失订单情况且荣数无法重推，可提供excel，通过接口使用备用商户号补单;
     * 2.接口仅限开发测试环境使用，发券数量和账号类型请按需修改（调接口之前须切换密钥: 将生产环境的<proxy_ylshshrs_test>的密钥更换成测试环境的<proxy_ylshshrs>密钥
     *
     * @param file
     * @return
     */
    @PostMapping("/importRsData")
    @Permission(action = Action.Skip)
    public BaseResponse importRsData(MultipartFile file) {
        BaseResponse baseResponse = new BaseResponse();
        if (file == null) {
            baseResponse.setMsg("导入文件不能为空");
            baseResponse.setCode(GlobalStatusCode.FAILED.getCode());
            return baseResponse;
        }
        try (InputStream inputStream = file.getInputStream();
             ExcelReader reader = ExcelUtil.getReader(inputStream);){
            reader.addHeaderAlias("sip订单编号", "sipOrderNo")
                    .addHeaderAlias("商户商品编号", "voucherTag")
                    .addHeaderAlias("收货账号", "accountNo")
                    .addHeaderAlias("账号信息", "accountInfo");

            List<RsRechargeBO> dtoList = reader.readAll(RsRechargeBO.class);
            for (int i = 0; i < dtoList.size(); i++) {
                RsRechargeBO item = dtoList.get(i);
                item.setNum(1);
                item.setAccountType("2");
                encrypt(item, i);
            }
        } catch (Exception e) {
            baseResponse.setMsg("补单异常");
            baseResponse.setCode(GlobalStatusCode.FAILED.getCode());
            return baseResponse;
        }

        baseResponse.setCode(GlobalStatusCode.SUCCESS.getCode());
        return baseResponse;
    }

    private void encrypt(RsRechargeBO request, int i) {
        HashMap map = new HashMap();
        try {
//            if (publicKey == null) {
//                publicKey = RSAHelper.getPemRSAPublicKey(configService.getByCode(sysId, RCAppConst.JINGXIN_RONGSHU_PUBLIC_KEY).getConfigValue());
//            }
//            if (privateKey == null) {
//                privateKey = RSAHelper.getPemRSAPrivateKey(configService.getByCode(sysId, RCAppConst.RONGSHU_PRIVATE_KEY).getConfigValue());
//            }
//            String data = JSONUtil.toJsonStr(request);
//            Map<String, Object> params = JSONObject.parseObject(data, Map.class);
//            Map<String, Object> sortedMap = params.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldVal, newVal) -> oldVal, LinkedHashMap::new));
//            String rsaData = RSAHelper.encrypt(publicKey, JSONUtil.toJsonStr(sortedMap));
//            String sign = RSAHelper.sign(JSONUtil.toJsonStr(sortedMap), privateKey);
//            map.put("data", rsaData);
//            map.put("sign", sign);
//            map.put("vendorNo", "proxy_ylshshrs_test");
//
//            log.info("-----------------------------------------");
//            log.info("第" + i + "条数据 : " + JSONUtil.toJsonStr(map));
//            String url = "https://rc.yesm.cn/api/rc/recharge/rsRecharge";
//            String url = "";
//            String result = HttpUtil.createPost(url).body(JSON.toJSONString(map)).timeout(5 * 1000).executeAsync().body();
//            log.info("结果:{}", result);
//            log.info("-----------------------------------------");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
