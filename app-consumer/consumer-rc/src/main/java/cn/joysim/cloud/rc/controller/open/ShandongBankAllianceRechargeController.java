package cn.joysim.cloud.rc.controller.open;

import cn.hutool.core.util.StrUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.common.consts.RCAppConst;
import cn.joysim.cloud.rc.common.exception.ShandongAllianceException;
import cn.joysim.cloud.rc.common.exception.ShandongAllianceRechargeException;
import cn.joysim.cloud.rc.common.model.pojo.enums.ProxyBalanceWayEnum;
import cn.joysim.cloud.rc.common.utils.BigDecimalUtil;
import cn.joysim.cloud.rc.model.dto.ChannelProxyInfoDTO;
import cn.joysim.cloud.rc.model.dto.ShandongAllianceProxyDTO;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.RechargeEncryptedRequest;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.RechargeEncryptedResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.RechargePlainTextRequest;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.ShandongRechargeBaseResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.helper.SafeTools;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.helper.ShandongBankAllianceRechargeHelper;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.request.directRecharge.AvailableBalanceRequest;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.request.directRecharge.ChargeOrderDetailRequest;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.request.directRecharge.ChargeOrderRequest;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.request.directRecharge.ProductInfoRequest;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.response.directRecharge.AvailableBalanceResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.response.directRecharge.ChargeOrderDetailResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.response.directRecharge.ChargeOrderResponse;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.response.directRecharge.ProductInfoResponse;
import cn.joysim.cloud.rc.service.ChannelProxyInfoService;
import cn.joysim.cloud.rc.service.ShandongAllianceProxyService;
import cn.joysim.cloud.rc.service.ShandongAllianceRechargeService;
import cn.joysim.cloud.rc.utils.ValidateUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.ValidationException;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequestMapping("/api/shandongAlliance/recharge")
@Slf4j
public class ShandongBankAllianceRechargeController {

    @Resource
    private ShandongAllianceProxyService shandongAllianceProxyService;

    @Resource
    private ChannelProxyInfoService channelProxyInfoService;


    @Resource
    private ShandongAllianceRechargeService shandongAllianceRechargeService;







    @SneakyThrows
    @RequestMapping(value = "/shandongRecharge", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public ShandongRechargeBaseResponse shandongRecharge(HttpServletRequest request, @RequestBody RechargeEncryptedRequest requestData) {
        String requestId = MDC.get(RCAppConst.MDC_REQUEST_ID);
        request.setAttribute("appId",requestData.getAppId());
        log.info("请求ID:{}",requestId);

        if (StrUtil.isBlank(requestData.getAppId())) {
            throw new ValidationException("appId为空");
        }
        if (StrUtil.isBlank(requestData.getEncryptData())) {
            throw new ValidationException("encryptData为空");
        }


        Optional<ShandongAllianceProxyDTO> merchantAccount = shandongAllianceProxyService.getByProxyCode(requestData.getAppId());

        if (!merchantAccount.isPresent()) {
            throw new ShandongAllianceRechargeException("商户账号不存在");
        }
        if (!merchantAccount.map(ShandongAllianceProxyDTO::getDirectRechargeSignKey).isPresent()&& merchantAccount.map(ShandongAllianceProxyDTO::getDirectRecharge3desKey).isPresent()) {
            throw new ShandongAllianceRechargeException("商户账号验签/解密配置异常");
        }

        String decrypt = "";
        ShandongBankAllianceRechargeHelper helper = new ShandongBankAllianceRechargeHelper(merchantAccount.map(ShandongAllianceProxyDTO::getDirectRecharge3desKey).get(), merchantAccount.map(ShandongAllianceProxyDTO::getDirectRechargeSignKey).get());
        try {
            decrypt= helper.decrypt(requestData.getEncryptData());
        } catch (Exception e) {
            throw new ShandongAllianceRechargeException("解密失败");
        }
        log.info("解密明文:{}",decrypt);

        RechargePlainTextRequest plainText = JSON.parse(decrypt, RechargePlainTextRequest.class);


        boolean timestampValid = helper.isTimestampValid(plainText.getTimestamp());
        if (!timestampValid) {
            throw new ShandongAllianceRechargeException("时间戳验证失败");
        }


        boolean b = false;
        try {
            String sign = helper.sign(plainText.getTransData(), plainText.getTransNonce(), plainText.getTimestamp());
            if (!sign.equals(plainText.getTransSign())) {
                throw new ShandongAllianceRechargeException("签名验证失败");
            }
        } catch (Exception e) {
            throw new ShandongAllianceRechargeException("签名验证失败");
        }


        log.info("明文:{}",plainText);

        String transCode = plainText.getTransCode();

        String jsonString = null;
        if (transCode.equals("80013")) {
            //联盟营销系统调用该接口查询直充&卡密权益的可用余额。
            AvailableBalanceResponse availableBalanceResponse = handle80013(requestData.getAppId(), JSON.parse(plainText.getTransData(), AvailableBalanceRequest.class));
            jsonString = JSON.toJSONString(availableBalanceResponse);

        } else if (transCode.equals("80014")) {
            //联盟营销系统调用该接口查询直充&卡密产品信息。
            ProductInfoResponse productInfoResponse = handle80014(requestData.getAppId(), JSON.parse(plainText.getTransData(), ProductInfoRequest.class));
            jsonString = JSON.toJSONString(productInfoResponse);
        } else if (transCode.equals("80015")) {
            //联盟营销系统调用该接口提交直充&卡密权益订单。
            ChargeOrderResponse response = handle80015(requestData.getAppId(), JSON.parse(plainText.getTransData(), ChargeOrderRequest.class));
            jsonString = JSON.toJSONString(response);
        } else if (transCode.equals("80016")) {
            //联盟营销系统调用该接口查询直充&卡密权益订单详情。
            ChargeOrderDetailResponse response = handle80016(requestData.getAppId(), JSON.parse(plainText.getTransData(), ChargeOrderDetailRequest.class));
            jsonString = JSON.toJSONString(response);
        }

        if (Objects.isNull(jsonString)) {
            throw new ShandongAllianceException("服务器异常");
        }
        RechargeEncryptedResponse encryptedData = new RechargeEncryptedResponse();
        encryptedData.setTransData(jsonString);
        encryptedData.setTransCode(transCode);
        encryptedData.setTimestamp(String.valueOf(System.currentTimeMillis()));
        encryptedData.setTransNonce(requestId);

        String reSign = helper.sign(encryptedData.getTransData(), "",encryptedData.getTimestamp());
        encryptedData.setTransSign(reSign);

        log.info("响应加密前信息#,requestData: {}",encryptedData);

        String encryptedDataValue = helper.encrypt(JSON.toJSONString(encryptedData));

        ShandongRechargeBaseResponse response = new ShandongRechargeBaseResponse();
        response.setAppId(requestData.getAppId());
        response.setCode(RCAppConst.SHAN_DONG_ALLIANCE_RECHARGE_SUCCESS_CODE);
        response.setEncryptData(encryptedDataValue);

        log.info("响应加密后的信息#,response:{}",JSON.toJSONString(response));

        return response;

    }


    private AvailableBalanceResponse handle80013(String appId, AvailableBalanceRequest parse) {
        ChannelProxyInfoDTO channelProxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(appId);

        ChannelProxyInfoDTO channelProxyInfoDTOValue = channelProxyInfoService.getById(channelProxyInfoDTO.getId());
        BigDecimal quota = channelProxyInfoDTOValue.getQuota();
        BigDecimal quotaUsed = channelProxyInfoDTOValue.getQuotaUsed();
        AvailableBalanceResponse availableBalanceResponse = new AvailableBalanceResponse();
        availableBalanceResponse.setAccountBalance(BigDecimalUtil.max(quota.subtract(quotaUsed),BigDecimal.ZERO));
        availableBalanceResponse.setCreditAmount(quota);
        ProxyBalanceWayEnum balanceWay = channelProxyInfoDTOValue.getBalanceWay();
        if (ProxyBalanceWayEnum.PRE_BALANCE_WAY.equals(balanceWay)) {
            availableBalanceResponse.setDebtFlag(0);
        } else if (ProxyBalanceWayEnum.AFTER_BALANCE_WAY.equals(balanceWay)) {
            availableBalanceResponse.setDebtFlag(1);
        }

        return availableBalanceResponse;
    }
    private ProductInfoResponse handle80014(String appId, ProductInfoRequest parse) {
        ValidateUtil.validate(parse);
        ProductInfoResponse response=shandongAllianceRechargeService.getDirectRechargeProductInfo(appId,parse);
        return response;
    }

    private ChargeOrderResponse handle80015(String appId, ChargeOrderRequest parse) {
        ValidateUtil.validate(parse);
        if (Objects.isNull(parse.getChargeaNum())) {
            parse.setChargeaNum(1);
        }
        if (!Integer.valueOf(1).equals(parse.getChargeaNum())) {
            throw new ShandongAllianceRechargeException("直充单次充值只允许充值数量为1");
        }

        ChargeOrderResponse response= shandongAllianceRechargeService.shandongRechargeChargeOrder(appId,parse);
        return response;
    }

    private ChargeOrderDetailResponse handle80016(String appId, ChargeOrderDetailRequest parse) {
        ValidateUtil.validate(parse);
        ChargeOrderDetailResponse response= shandongAllianceRechargeService.shandongRechargeChargeOrderDetail(appId,parse);
        return response;

    }







}
