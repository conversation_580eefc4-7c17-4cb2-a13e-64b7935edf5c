package cn.joysim.cloud.rc.controller.open;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import cn.joysim.cloud.common.annotation.Action;
import cn.joysim.cloud.common.annotation.Permission;
import cn.joysim.cloud.common.util.IpAddrUtil;
import cn.joysim.cloud.common.util.JSON;
import cn.joysim.cloud.rc.common.exception.ShandongAllianceException;
import cn.joysim.cloud.rc.common.model.pojo.enums.CouponState;
import cn.joysim.cloud.rc.common.utils.IpWhite;
import cn.joysim.cloud.rc.model.dto.ChannelProxyInfoDTO;
import cn.joysim.cloud.rc.model.dto.RechargeCouponDTO;
import cn.joysim.cloud.rc.model.dto.ShandongAllianceProxyDTO;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.data.SendCouponResultDTO;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.helper.ShandongBankAllianceCouponHelper;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.request.coupon.*;
import cn.joysim.cloud.rc.model.dto.shandongAlliance.response.coupon.*;
import cn.joysim.cloud.rc.model.dto.virtualCoupon.RechargeVirtualCouponOrderDTO;
import cn.joysim.cloud.rc.service.ChannelProxyInfoService;
import cn.joysim.cloud.rc.service.ChannelProxyItemInfoService;
import cn.joysim.cloud.rc.service.ShandongAllianceProxyService;
import cn.joysim.cloud.rc.service.ShandongAllianceRechargeService;
import cn.joysim.cloud.rc.service.virtualCoupon.RechargeCouponService;
import cn.joysim.cloud.rc.service.virtualCoupon.RechargeVirtualCouponOrderService;
import cn.joysim.cloud.rc.utils.ValidateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping("/api/shandongAlliance/coupon")
@Slf4j
public class ShandongBankAllianceCouponController {


    @Resource
    private ShandongAllianceProxyService shandongAllianceProxyService;


    @Resource
    private ChannelProxyInfoService channelProxyInfoService;


    @Resource
    private ChannelProxyItemInfoService channelProxyItemInfoService;


    @Resource
    private ShandongAllianceRechargeService shandongAllianceRechargeService;

    @Resource
    private RechargeVirtualCouponOrderService rechargeVirtualCouponOrderService;

    @Resource
    private RechargeCouponService rechargeCouponService;

    @SneakyThrows
    private void verifySign(String body) {

        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> map = mapper.readValue(body, TypeFactory.defaultInstance().constructMapType(Map.class, String.class, Object.class));
        Object merchantAccountId = map.get("merchantAccount");
        if (Objects.isNull(merchantAccountId)) {
            throw new ShandongAllianceException("merchantAccount不允许为空");
        }

        Optional<ShandongAllianceProxyDTO> merchantAccount = shandongAllianceProxyService.getByProxyCode((String)merchantAccountId);
        if (!merchantAccount.isPresent()) {
            throw new ShandongAllianceException("商户账号不存在");
        }
        if (!merchantAccount.map(ShandongAllianceProxyDTO::getCouponSignKey).isPresent()) {
            throw new ShandongAllianceException("商户账号验签配置异常");
        }
        Object providedSignature = map.get("sign");
        if (Objects.isNull(providedSignature)) {
            throw new ShandongAllianceException("sign不允许为空");
        }
        String generatedSignature = new ShandongBankAllianceCouponHelper(merchantAccount.map(ShandongAllianceProxyDTO::getCouponSignKey).get()).generateSignature(map);
        log.info("提供的签名:{},获取的签名:{}",providedSignature,generatedSignature);
        if (!providedSignature.equals(generatedSignature)) {
            throw new ShandongAllianceException("商户账号验签校验失败");
        }
    }

    private void verifyIP(HttpServletRequest request, String proxyCode) {
        String realIp = IpAddrUtil.getRealIp(request);
        //查询下游商户信息
        ChannelProxyInfoDTO proxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(proxyCode);
        if (null == proxyInfoDTO || !proxyInfoDTO.getEnable()) {
            throw new ShandongAllianceException("商户信息不存在");
        }
        //白名单校验
        String[] ips = StringUtils.hasText(proxyInfoDTO.getServerIps()) ? proxyInfoDTO.getServerIps().split(";") : null;
        if (null == ips || !IpWhite.isIpInWhitelist(realIp,ips)) {
            throw new ShandongAllianceException("IP白名单不匹配");
        }
    }


    /**
     * Merchant/TypeList
     * @param
     * @return
     */
    @RequestMapping(value = "/Merchant/TypeList", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public MerchantTypeListResponse merchantTypeList(HttpServletRequest request) {
        String body = ServletUtil.getBody(request);
        log.info("山东联盟获取商品列表信息body: {}",body);
        //验证签名
        verifySign(body);

        MerchantTypeListRequest requestData = JSON.parse(body, MerchantTypeListRequest.class);


        ValidateUtil.validate(requestData);

        log.info("山东联盟获取商品列表信息requestData: {}",requestData);
        verifyIP(request, requestData.getMerchantAccount());

        MerchantTypeListResponse merchantTypeListResponse = new MerchantTypeListResponse();

        ChannelProxyInfoDTO proxyInfoDTO = channelProxyInfoService.getInfoByCodeCache(requestData.getMerchantAccount());

        List<MerchantTypeListResponse.MerchantType> content =  channelProxyItemInfoService.getCouponProxyItems(proxyInfoDTO.getId());
        merchantTypeListResponse.setContent(content);

        merchantTypeListResponse.ok();
        log.info("山东联盟获取商品列表信息response: {}",JSONUtil.toJsonStr(merchantTypeListResponse));
        return merchantTypeListResponse;
    }


    /**
     * /Merchant/SendCoupon
     * @param
     * @return
     */
    @RequestMapping(value = "/Merchant/SendCoupon", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public SendCouponResponse sendCoupon(HttpServletRequest request) {



        String body = ServletUtil.getBody(request);
        log.info("山东联盟发券信息body: {}",body);
        //验证签名
        verifySign(body);

        SendCouponRequest requestData=JSON.parse(body,SendCouponRequest.class);

        ValidateUtil.validate(requestData);

        verifyIP(request, requestData.getMerchantAccount());

        long time = System.currentTimeMillis();


        SendCouponResultDTO sendCouponResultDTO =shandongAllianceRechargeService.shandongRechargeSendCoupon(requestData);
        Long couponId = sendCouponResultDTO.getCouponId();

        //4.下单
        SendCouponResponse result = new SendCouponResponse();

        RechargeCouponDTO couponDTO = rechargeCouponService.getById(couponId);
        if (Arrays.asList(CouponState.SENDING,CouponState.TO_BE_SEND,CouponState.FAILURE,CouponState.FAILURE_AND_NEED_REPLACE_ID_RETRY,CouponState.INTERCEPT,CouponState.CANCEL).contains(couponDTO.getState())) {
            //发送失败
            SendCouponResponse sendCouponResponse = new SendCouponResponse();
            sendCouponResponse.setBuyerOrderId(requestData.getOrderId());
            sendCouponResponse.setTransactionId(couponDTO.getId().toString());

            if (CouponState.FAILURE.equals(couponDTO.getState())) {
                sendCouponResponse.setMessage(couponDTO.getErrorMsg());
            }
            sendCouponResponse.fail();

            result= sendCouponResponse;
        }else{

            //发送成功
            Long couponOrderId = couponDTO.getCouponOrderId();
            RechargeVirtualCouponOrderDTO orderDTO = rechargeVirtualCouponOrderService.getById(couponOrderId);
            SendCouponResponse sendCouponResponse = new SendCouponResponse();
            sendCouponResponse.setBuyerOrderId(requestData.getOrderId());
            sendCouponResponse.setTransactionId(couponDTO.getId().toString());
            sendCouponResponse.setCouponNo(couponDTO.getSupplierCouponNo());
            sendCouponResponse.setOrderTime(orderDTO.getCreateTime());

            sendCouponResponse.setUsableTime(couponDTO.getAvailableEndTime());
            sendCouponResponse.ok();

            result= sendCouponResponse;
        }

        long workTime = System.currentTimeMillis() - time;
        if (workTime > 3000) {
            log.error("山东联盟发券请求耗时异常:{}",workTime);
        }
        log.info("山东联盟发券下单总耗时：{},返回结果:{}",workTime, JSONUtil.toJsonStr(result));

        result.ok();
        return result;
    }


    /**
     * 山东联盟获取卡券列表信息
     * Merchant/GetCouponList
     * @param
     * @return
     */
    @RequestMapping(value = "/Merchant/GetCouponList", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public GetCouponListResponse getCouponList(HttpServletRequest request) {

        log.error("山东联盟暂未使用接口");
        String body = ServletUtil.getBody(request);
        log.info("山东联盟获取卡券列表信息body: {}",body);
        //验证签名
        verifySign(body);

        GetCouponListRequest requestData = JSON.parse(body, GetCouponListRequest.class);

        ValidateUtil.validate(requestData);

        log.info("山东联盟获取卡券列表信息,requestData: {}",requestData);

        verifyIP(request, requestData.getMerchantAccount());

        GetCouponListResponse response=shandongAllianceRechargeService.getCouponList(requestData);

        response.ok();
        log.info("山东联盟获取卡券列表信息response: {}",JSONUtil.toJsonStr(response));
        return response;
    }


    /**
     * 山东联盟获取订单信息
     * Merchant/MerchantOrderInfo
     * @return
     */
    @RequestMapping(value = "/Merchant/MerchantOrderInfo", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public GetMerchantOrderInfoResponse merchantOrderInfo(HttpServletRequest request) {

        String body = ServletUtil.getBody(request);
        log.info("山东联盟获取订单信息body: {}",body);
        //验证签名
        verifySign(body);

        GetMerchantOrderInfoRequest requestData=JSON.parse(body,GetMerchantOrderInfoRequest.class);

        ValidateUtil.validate(requestData);

        log.info("山东联盟获取订单信息,requestData: {}",requestData);

        verifyIP(request, requestData.getMerchantAccount());


        GetMerchantOrderInfoResponse response=shandongAllianceRechargeService.merchantOrderInfo(requestData);
        response.ok();
        log.info("山东联盟获取订单信息response: {}",JSONUtil.toJsonStr(response));
        return response;
    }

    /**
     * 获取账单接口
     * Merchant/MerchantOrderInfo
     * @param
     * @return
     */
    @RequestMapping(value = "/Merchant/GetMerchantBillList", produces = "application/json;charset=UTF-8")
    @Permission(action = Action.Skip)
    public GetMerchantBillListResponse getMerchantBillList(HttpServletRequest request) {

        String body = ServletUtil.getBody(request);
        log.info("山东联盟获取账单接口body: {}",body);
        //验证签名
        verifySign(body);

        GetMerchantBillListRequest requestData = JSON.parse(body, GetMerchantBillListRequest.class);
        log.info("山东联盟获取账单接口request: {},requestData: {}",request,requestData);

        ValidateUtil.validate(requestData);

        verifyIP(request, requestData.getMerchantAccount());

        GetMerchantBillListResponse billListResponse=shandongAllianceRechargeService.getMerchantBillList(requestData);
        billListResponse.ok();
        log.info("山东联盟获取账单接口response: {}",JSONUtil.toJsonStr(billListResponse));
        return billListResponse;
    }


}
