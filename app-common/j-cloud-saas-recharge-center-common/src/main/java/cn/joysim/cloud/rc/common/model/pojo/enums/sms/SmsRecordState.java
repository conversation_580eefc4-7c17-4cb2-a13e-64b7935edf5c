package cn.joysim.cloud.rc.common.model.pojo.enums.sms;

import cn.joysim.cloud.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

public enum SmsRecordState implements ValueEnum {

    UN_SEND(0,"未发送"),
    SENDING(1, "发送中"),
    SEND_SUCCESS(2, "发送成功"),
    SEND_FAILED(3, "发送失败"),
    OT_SEND(4, "无需发送"),
    WAIT_CONFIRM(5, "等待确认结果");

    SmsRecordState(Integer code, String text) {
        this.code = code;
        this.text = text;
    }

    @EnumValue
    private Integer code;
    private String text;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    @JsonCreator
    public static SmsRecordState fromCode(Integer code) {
        for (SmsRecordState value : SmsRecordState.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}