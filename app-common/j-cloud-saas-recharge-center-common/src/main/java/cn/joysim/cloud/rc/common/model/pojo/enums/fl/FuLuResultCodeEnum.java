package cn.joysim.cloud.rc.common.model.pojo.enums.fl;

import cn.joysim.cloud.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;

import java.util.Arrays;
import java.util.List;

/**
 * 福禄充值接口返回状态码  invalid
 */
public enum FuLuResultCodeEnum implements ValueEnum {
    SUCCESS_CODE(0,"接口调用成功"),
    API_NAME_IS_NULL(1000,"必须传入API接口名称"),
    INVALID_API_NAME(1001,"无效的API接口名称"),
    TIMESTAMP_IS_NULL(1002,"必须传入时间戳"),
    TIMESTAMP_FORMAT_ERROR(1003,"时间戳格式错误"),
    TIMESTAMP_INVALID(1004,"时间戳已超过有效期"),
    APP_KEY_IS_NULL(1005,"必须传入app_key"),
    APP_KEY_INVALID(1006,"无效的app_key"),
    VERSION_IS_NULL(1007,"必须传入版本号"),
    VERSION_ERROR(1008,"版本号错误"),
    FORMAT_IS_NULL(1009,"format必填"),
    FORMAT_ERROR(1010,"format格式错误"),
    CHAR_SET_IS_NULL(1011,"必须传入编码格式"),
    CHAR_SET_ERROR(1012,"编码格式错误"),
    SIGN_TYPE_IS_NULL(1013,"必须传入签名加密类型"),
    SIGN_TYPE_ERROR(1014,"签名加密类型错误"),
    SIGN_IS_NULL(1015,"必须传入签名"),
    SIGN_ERROR(1016,"签名错误"),
    BIZ_CONTENT_IS_NULL(1017,"必须传入请求参数集合"),
    PARAM_IS_NULL(1018,"缺少必要参数"),
    IP_LIMIT(1019,"访问IP不在IP白名单内"),
    MERCHANT_NOT_EXIST(2000,"商户不存在"),
    MERCHANT_IS_BAN(2001,"商户已被禁用"),
    INVALID_MERCHANT(2002,"无效的商户或应用"),


    MERCHANT_CONFIG_ERROR(2003,"商户或应用配置异常"),
    MERCHANT_BALANCE_NOT_ENOUGH(2004,"商户余额不足"),
    PRODUCT_IS_NULL(3000,"必须传入商品编号"),
    PRODUCT_NOT_EXIST(3001,"商品不存在或无法购买"),
    PRODUCT_DOWN(3002,"商品已下架"),
    PRODUCT_MAINTAIN(3003,"商品维护中"),
    PRODUCT_MAINTAIN_TIME(3004,"商品在维护期内"),
    PRODUCT_STOCK_NOT_ENOUGH(3005,"商品库存不足"),
    PRODUCT_TYPE_ERROR(3008,"商品类型错误"),
    OUT_ORDER_NOT_NULL(4000,"必须传入外部订单号"),

    BUY_NUM_ERROR(4001,"购买数量必须大于0"),
    ACCOUNT_NOT_NULL(4002,"必须传入充值账号"),
    BLACK_USER(4004,"充值账号在黑名单中"),

    ADD_ORDER_FAIL(4008,"添加订单失败"),
    ORDER_TIME_OUT(4009,"执行下单超时"),
    OUT_ORDER_IS_EXIST(4010,"外部订单号已存在"),
    ORDER_NOT_EXIST(4011,"订单不存在"),
    QUERY_ERROR(4012,"查询异常，请重试"),
    SYSTEM_ERROR(5000,"系统异常，订单可疑"),
    SYSTEM_ERROR_FAIL(5001,"系统异常");
    @EnumValue
    private Integer code;

    private String text;

    FuLuResultCodeEnum(Integer code,String text){
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public static FuLuResultCodeEnum fromCode(Integer code){
        for (FuLuResultCodeEnum codeEnum : FuLuResultCodeEnum.values()){
            if (codeEnum.getCode().equals(code)){
                return codeEnum;
            }
        }
        return null;
    }

    public static boolean isFail(Integer code){
        FuLuResultCodeEnum codeEnum = fromCode(code);
        if (null == codeEnum){
            return false;
        }
        List<FuLuResultCodeEnum> failList = Arrays.asList(API_NAME_IS_NULL, INVALID_API_NAME, TIMESTAMP_IS_NULL, TIMESTAMP_FORMAT_ERROR, TIMESTAMP_INVALID, APP_KEY_IS_NULL,
                APP_KEY_INVALID, VERSION_IS_NULL, VERSION_ERROR, FORMAT_IS_NULL, FORMAT_ERROR, CHAR_SET_IS_NULL, CHAR_SET_ERROR, SIGN_TYPE_IS_NULL, SIGN_TYPE_ERROR, SIGN_IS_NULL, SIGN_ERROR, BIZ_CONTENT_IS_NULL,
                PARAM_IS_NULL, IP_LIMIT, MERCHANT_NOT_EXIST, MERCHANT_IS_BAN, INVALID_MERCHANT, MERCHANT_CONFIG_ERROR, MERCHANT_BALANCE_NOT_ENOUGH, PRODUCT_IS_NULL, PRODUCT_NOT_EXIST, PRODUCT_DOWN,
                PRODUCT_MAINTAIN, PRODUCT_MAINTAIN_TIME, PRODUCT_STOCK_NOT_ENOUGH, PRODUCT_TYPE_ERROR, OUT_ORDER_NOT_NULL, BUY_NUM_ERROR, ACCOUNT_NOT_NULL, BLACK_USER);
        return failList.contains(codeEnum);
    }
}
