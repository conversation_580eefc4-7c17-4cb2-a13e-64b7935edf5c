package cn.joysim.cloud.rc.common.model.pojo.enums.lsxd;

/**
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-01-09 15:22
 * @Description: 蓝色兄弟返回状态码
 */
public enum LsXdResultCodeEnum {
    SUCCESS_CODE("0000","成功"),
    IP_LIMIT_CODE("1000","ip未绑定或绑定失败"),
    MISSING_PARAMETERS_CODE("1001","参数异常"),
    INVALID_MERCHANT_CODE("1002","无效商户信息"),
    INVALID_SIGNATURE_CODE("1003","签名校验失败"),
    REQUEST_EXPIRATION_CODE("1004","请求时间过期"),
    ORDER_REPEAT_CODE("1005","订单重复"),

    INVALID_ITEM_CODE("1006","商品未开通或商品暂停使用"),
    ITEM_PRICE_INVALID_CODE("1007","商品价格无效"),
    INSUFFICIENT_BALANCE_CODE("1008","余额不足"),

    INTERFACE_ADJUSTMENT_CODE("1009","商品映射无效"),
    INTERFACE_PRICE_ADJUSTMENT_CODE("1010","映射价格无效"),
    ACCOUNT_FORMAT_MATCHING("1011","充值账号格式不匹配"),

    NO_ORDER("1012","无订单信息"),

    /**
     * 异常错误，建议人工处理或查询订单状态
     */
    UNKNOWN_ERROR("1999","异常错误"),
    /**
     * 下单成功，不代表充值成功
     */
    ORDER_SUCCESS_CODE("2000","下单成功"),

    REQUEST_LIMIT_CODE("1099","请求限制频率,请稍后再试")
    ;

    private String code;

    /**
     * 错误信息
     */
    private String msg;


    LsXdResultCodeEnum(String code,String msg){
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}