package cn.joysim.cloud.rc.common.model.pojo.enums.cjxx;

import cn.joysim.cloud.common.model.enums.ValueEnum;

/**
 * @author:l<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-01-24 15:56
 * @Description: 超级猩猩接口返回码枚举
 */
public enum CjXxResultCodeEnum implements ValueEnum {
    SUCCESS_CODE(0,"业务成功"),
    FAILED_CODE(-1,"业务执行失败"),
    PARAM_ERROR(13,"参数异常"),
    OPERATE_FORBID(14,"操作被禁止"),
    UN_KNOWN_ERROR(500,"服务未知异常"),
    ORDER_NOT_EXIST(1007,"订单不存在"),
    INVALID_ORDER_ID(13101,"订单流水号无效"),
    INVALID_ORDER_CALL_BCK_PARAM(13105,"订单回调参数无效"),
    INVALID_NOTIFY_ERROR(-10103,"订单回调失败")
    ;

    private Integer code;

    private String text;


    CjXxResultCodeEnum(Integer code,String text){
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setText(String text) {
        this.text = text;
    }
}