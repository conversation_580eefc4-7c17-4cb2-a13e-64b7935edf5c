package cn.joysim.cloud.rc.common.model.pojo.enums.yd;

import cn.joysim.cloud.common.model.enums.ValueEnum;

/**
 * 易点卡券充值接口返回状态码
 */
public enum YdResultCodeEnum implements ValueEnum {

    SUCCESS_CODE(0,"请求成功"),
    SYSTEM_ERROR(10001,"系统异常"),
    INVALID_SIGN(10002,"无效签名"),
    INVALID_PARAM(10003,"无效公共参数"),
    PARAM_ERROR(10004	,"参数错误"),
    ILLEGAL_IP(10005,"不合法的ip"),
    REQUEST_METHOD_ERROR(10006,"请求方式错误"),
    REPEAT_REQUEST(10007,"重复请求"),
    PERMISSION_DENIED(10008,"没有API权限"),
    API_METHOD_NOT_EXIST(10009,"API方法不存在"),
    REQUEST_FORMAT_ERROR(10010,"请求格式错误"),
    ILLEGAL_REQUEST(10011,"非法请求"),
    BALANCE_NOT_ENOUGH(20001,"客户帐户余额不足"),
    PRODUCT_NOT_EXIST(20002,"商品不存在"),
    USER_NOT_EXIST(20003,"客户不存在"),
    USER_DETAIL_NOT_EXIST(20004,"客户明细不存在"),
    ORDER_NOT_EXIST(20005,"订单不存在"),
    CODE_NOT_EXIST(20006,"券码不存在"),
    ORDER_EXIST(20007,"订单已存在"),
    ORDER_FAIL(20008,"下单失败"),
    PRODUCT_STOCK_NOT_ENOUGH(20009,"商品库存不足"),
    PARAM_ERROR_2(50020,"参数错误"),
    PRODUCT_STOCK_NOT_ENOUGH_2(50030,"库存不足"),
    OTHER_ERROR(99999,"其他异常");



    private Integer code;

    private String text;

    YdResultCodeEnum(Integer code,String text){
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setText(String text) {
        this.text = text;
    }
}
