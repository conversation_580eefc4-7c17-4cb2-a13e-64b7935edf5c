package cn.joysim.cloud.rc.common.model.pojo.enums.yd;

import cn.joysim.cloud.common.model.enums.ValueEnum;

public enum YdOrderStatusEnum implements ValueEnum {

    ORDER_REGISTER_CODE(0,"订单登记"),
    OBLIGATION_CODE(1,"待付款"),
    WAIT_SENT_CODE(2,"待发货"),
    ORDER_SUCCESS_CODE(3,"已完成"),
    ORDER_CLOSE_CODE(4,"已关闭"),
    ORDER_FAIL_CODE(5,"订单失败"),
    READY_SEND_CODE(6,"已出库待发放"),
    ;
    private Integer code;

    private String text;

    YdOrderStatusEnum(Integer code,String text){
        this.code = code;
        this.text = text;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getText() {
        return text;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public void setText(String text) {
        this.text = text;
    }
}
