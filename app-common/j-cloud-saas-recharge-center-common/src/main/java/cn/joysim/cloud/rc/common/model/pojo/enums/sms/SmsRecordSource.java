package cn.joysim.cloud.rc.common.model.pojo.enums.sms;

import cn.joysim.cloud.common.model.enums.ValueEnum;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;

public enum SmsRecordSource implements ValueEnum {

    GENERAL_ORDER_NOTIFY(0,"通用充值订单充值结果通知","generalRechargeSmsHandle"),


    CARD_CIPHER_ORDER_NOTIFY(1,"卡密充值订单充值结果通知","cardCipherRechargeSmsHandle"),

    COUPON_ORDER_NOTIFY(2,"卡券充值订单充值结果通知","couponRechargeSmsHandle")
    ;

    SmsRecordSource(Integer code, String text,String stateChangeHandle) {
        this.code = code;
        this.text = text;
        this.stateChangeHandle=stateChangeHandle;
    }

    @EnumValue
    private Integer code;
    private String text;

    private String stateChangeHandle;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getText() {
        return this.text;
    }

    public String getStateChangeHandle() {
        return stateChangeHandle;
    }

    @JsonCreator
    public static SmsRecordSource fromCode(Integer code) {
        for (SmsRecordSource value : SmsRecordSource.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}