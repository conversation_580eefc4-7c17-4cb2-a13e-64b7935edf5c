package cn.joysim.cloud.rc.common.model.pojo.enums.lsxd;

/**
 * @author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-01-09 15:16
 * @Description: 蓝色兄弟供应商订单状态枚举类
 */
public enum LsXdOrderStateEnum {

    RECHARGE_SUCCESS("01","充值成功"),
    RECHARGE_PENDING("02","充值处理中"),
    RECHARGE_FAIL("03","充值失败"),
    RECHARGE_EXCEPTION("04","充值异常，处理中");


    private String code;

    /**
     * 错误信息
     */
    private String msg;

    LsXdOrderStateEnum(String code,String msg){
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}